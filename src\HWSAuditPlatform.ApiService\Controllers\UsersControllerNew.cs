using Asp.Versioning;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Users.DTOs;
using HWSAuditPlatform.Application.Users.Services;

namespace HWSAuditPlatform.ApiService.Controllers;

/// <summary>
/// Controller for user management operations using service pattern (replaces MediatR)
/// </summary>
[Route("api/v{version:apiVersion}/users-new")]
[ApiVersion("1.0")]
public class UsersControllerNew : BaseController
{
    private readonly IUserService _userService;

    public UsersControllerNew(IUserService userService, ILogger<UsersControllerNew> logger) 
        : base(null!, logger) // Temporarily pass null for mediator since we're not using it
    {
        _userService = userService;
    }

    /// <summary>
    /// Get a paginated list of users
    /// </summary>
    /// <param name="request">Query parameters for filtering and pagination</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Paginated list of users</returns>
    [HttpGet]
    [Authorize(Policy = "SystemManagerOrAbove")]
    [ProducesResponseType(typeof(PaginatedResult<UserSummaryDto>), 200)]
    public async Task<ActionResult<PaginatedResult<UserSummaryDto>>> GetUsers(
        [FromQuery] GetUsersRequest request,
        CancellationToken cancellationToken)
    {
        Logger.LogInformation("Getting users with request: {@Request}", request);
        var result = await _userService.GetUsersAsync(request, cancellationToken);
        return Success(result);
    }

    /// <summary>
    /// Get a specific user by ID
    /// </summary>
    /// <param name="id">User ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>User details</returns>
    [HttpGet("{id}")]
    [Authorize(Policy = "SystemManagerOrAbove")]
    [ProducesResponseType(typeof(UserDto), 200)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<UserDto>> GetUser(string id, CancellationToken cancellationToken)
    {
        Logger.LogInformation("Getting user with ID: {UserId}", id);
        var result = await _userService.GetUserAsync(id, cancellationToken);
        return Success(result);
    }

    /// <summary>
    /// Create a new user
    /// </summary>
    /// <param name="request">User creation request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Created user ID</returns>
    [HttpPost]
    [Authorize(Policy = "SystemManagerOrAbove")]
    [ProducesResponseType(typeof(string), 201)]
    [ProducesResponseType(400)]
    public async Task<ActionResult<string>> CreateUser(
        [FromBody] CreateUserRequest request,
        CancellationToken cancellationToken)
    {
        Logger.LogInformation("Creating user: {@Request}", request);
        var userId = await _userService.CreateUserAsync(request, cancellationToken);
        return CreatedAtAction(nameof(GetUser), new { id = userId }, userId);
    }

    /// <summary>
    /// Update an existing user
    /// </summary>
    /// <param name="id">User ID</param>
    /// <param name="request">User update request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>No content</returns>
    [HttpPut("{id}")]
    [Authorize(Policy = "SystemManagerOrAbove")]
    [ProducesResponseType(204)]
    [ProducesResponseType(400)]
    [ProducesResponseType(404)]
    public async Task<IActionResult> UpdateUser(
        string id,
        [FromBody] UpdateUserRequest request,
        CancellationToken cancellationToken)
    {
        if (id != request.Id)
        {
            return BadRequest("ID in URL does not match ID in request body");
        }

        Logger.LogInformation("Updating user {UserId}: {@Request}", id, request);
        await _userService.UpdateUserAsync(request, cancellationToken);
        return NoContent();
    }

    /// <summary>
    /// Delete a user (soft delete)
    /// </summary>
    /// <param name="id">User ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>No content</returns>
    [HttpDelete("{id}")]
    [Authorize(Policy = "SystemManagerOrAbove")]
    [ProducesResponseType(204)]
    [ProducesResponseType(404)]
    public async Task<IActionResult> DeleteUser(string id, CancellationToken cancellationToken)
    {
        Logger.LogInformation("Deleting user: {UserId}", id);
        await _userService.DeleteUserAsync(id, cancellationToken);
        return NoContent();
    }

    /// <summary>
    /// Get all available roles
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of roles</returns>
    [HttpGet("roles")]
    [Authorize(Policy = "SystemManagerOrAbove")]
    [ProducesResponseType(typeof(List<RoleDto>), 200)]
    public async Task<ActionResult<List<RoleDto>>> GetRoles(CancellationToken cancellationToken)
    {
        Logger.LogInformation("Getting all roles");
        var result = await _userService.GetRolesAsync(cancellationToken);
        return Success(result);
    }

    /// <summary>
    /// Search for users with fuzzy matching
    /// </summary>
    /// <param name="searchTerm">Search term</param>
    /// <param name="maxResults">Maximum number of results (default: 5)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of matching users</returns>
    [HttpGet("search")]
    [Authorize(Policy = "ProcessOwnerOrAbove")]
    [ProducesResponseType(typeof(List<UserSummaryDto>), 200)]
    public async Task<ActionResult<List<UserSummaryDto>>> SearchUsers(
        [FromQuery] string searchTerm,
        [FromQuery] int maxResults = 5,
        CancellationToken cancellationToken = default)
    {
        Logger.LogInformation("Searching users with term: {SearchTerm}", searchTerm);
        var result = await _userService.SearchUsersAsync(searchTerm, maxResults, cancellationToken);
        return Success(result);
    }

    /// <summary>
    /// Synchronize users from Active Directory
    /// </summary>
    /// <param name="request">Sync request parameters</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Sync results</returns>
    [HttpPost("sync-from-ad")]
    [Authorize(Policy = "DevAdminOnly")]
    [ProducesResponseType(typeof(SyncUsersFromAdResult), 200)]
    public async Task<ActionResult<SyncUsersFromAdResult>> SyncUsersFromAd(
        [FromBody] SyncUsersFromAdRequest request,
        CancellationToken cancellationToken)
    {
        Logger.LogInformation("Syncing users from AD: {@Request}", request);
        var result = await _userService.SyncUsersFromAdAsync(request, cancellationToken);
        return Success(result);
    }
}
