# PowerShell script to temporarily fix controller compilation errors
# This script will comment out problematic imports and methods to allow compilation

$controllersPath = "src\HWSAuditPlatform.ApiService\Controllers"

# List of controllers to fix (excluding already fixed ones)
$controllers = @(
    "AreaResponsibilityController.cs",
    "CorrectionRequestsController.cs", 
    "CorrectiveActionsController.cs",
    "FindingCategoriesController.cs",
    "FindingsController.cs",
    "OrganizationController.cs",
    "ProcessOwnerAssignmentsController.cs",
    "RecurringAuditsController.cs",
    "TemplateAccessController.cs",
    "TemplatesController.cs"
)

foreach ($controller in $controllers) {
    $filePath = Join-Path $controllersPath $controller
    if (Test-Path $filePath) {
        Write-Host "Processing $controller..."
        
        # Read the file content
        $content = Get-Content $filePath -Raw
        
        # Comment out problematic using statements
        $content = $content -replace 'using HWSAuditPlatform\.Application\..*\.Commands\..*', '// $&'
        $content = $content -replace 'using HWSAuditPlatform\.Application\..*\.Queries\..*', '// $&'
        
        # Remove IMediator from constructor parameters
        $content = $content -replace 'IMediator mediator,\s*', ''
        $content = $content -replace ',\s*IMediator mediator', ''
        $content = $content -replace 'IMediator mediator', ''
        
        # Update base constructor calls
        $content = $content -replace ': base\(mediator, logger\)', ': base(logger)'
        $content = $content -replace ': base\(logger, mediator\)', ': base(logger)'
        
        # Write the modified content back
        Set-Content $filePath $content -NoNewline
        
        Write-Host "Fixed $controller"
    }
}

Write-Host "Controller fixes completed!"
