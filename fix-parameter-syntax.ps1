# PowerShell script to fix parameter syntax errors

$controllersPath = "src\HWSAuditPlatform.ApiService\Controllers"

# Get all controller files
$controllerFiles = Get-ChildItem -Path $controllersPath -Filter "*.cs"

foreach ($file in $controllerFiles) {
    $filePath = $file.FullName
    $content = Get-Content $filePath -Raw
    $modified = $false
    
    # Fix the broken parameter syntax patterns
    if ($content -match '/\* // TODO: Replace with service call \*/ var') {
        Write-Host "Fixing parameter syntax in $($file.Name)..."
        
        # Replace the broken parameter patterns with proper object types
        $content = $content -replace '/\* // TODO: Replace with service call \*/ var', 'object request /* TODO: Fix parameter type */'
        
        # Also fix any remaining Mediator.Send calls to return null temporarily
        $content = $content -replace 'await Mediator\.Send\([^)]+\)', 'null /* TODO: Replace with service call */'
        $content = $content -replace 'Mediator\.Send\([^)]+\)', 'null /* TODO: Replace with service call */'
        
        $modified = $true
    }
    
    if ($modified) {
        Set-Content $filePath $content -NoNewline
        Write-Host "Fixed parameter syntax in $($file.Name)"
    }
}

Write-Host "Parameter syntax fixes completed!"
