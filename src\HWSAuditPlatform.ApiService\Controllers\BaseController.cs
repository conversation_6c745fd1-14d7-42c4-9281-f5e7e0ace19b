using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using HWSAuditPlatform.ApiService.Models;

namespace HWSAuditPlatform.ApiService.Controllers;

/// <summary>
/// Base controller with common functionality
/// </summary>
[ApiController]
[Authorize]
[Produces("application/json")]
[ProducesResponseType(typeof(ApiErrorResponse), 400)]
[ProducesResponseType(typeof(ApiErrorResponse), 401)]
[ProducesResponseType(typeof(ApiErrorResponse), 403)]
[ProducesResponseType(typeof(ApiErrorResponse), 500)]
public abstract class BaseController : ControllerBase
{
    protected readonly ILogger Logger;

    protected BaseController(ILogger logger)
    {
        Logger = logger;
    }

    /// <summary>
    /// Creates a successful response with data
    /// </summary>
    protected ActionResult<T> Success<T>(T data, string? message = null)
    {
        return Ok(ApiResponse<T>.SuccessResult(data, message));
    }

    /// <summary>
    /// Creates a successful response without data
    /// </summary>
    protected ActionResult Success(string? message = null)
    {
        return Ok(ApiResponse<object>.SuccessResult(message));
    }

    /// <summary>
    /// Creates a created response with location
    /// </summary>
    protected ActionResult<T> Created<T>(string actionName, object routeValues, T data, string? message = null)
    {
        return CreatedAtAction(actionName, routeValues, ApiResponse<T>.SuccessResult(data, message));
    }

    /// <summary>
    /// Creates a no content response
    /// </summary>
    protected ActionResult NoContentSuccess(string? message = null)
    {
        if (!string.IsNullOrEmpty(message))
        {
            return Ok(ApiResponse<object>.SuccessResult(message));
        }
        return NoContent();
    }

    /// <summary>
    /// Gets the current user ID from claims
    /// </summary>
    protected string? GetCurrentUserId()
    {
        return User?.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
    }

    /// <summary>
    /// Gets the current username from claims
    /// </summary>
    protected string? GetCurrentUsername()
    {
        return User?.FindFirst(System.Security.Claims.ClaimTypes.Name)?.Value;
    }

    /// <summary>
    /// Gets the current user's role from claims
    /// </summary>
    protected string? GetCurrentUserRole()
    {
        return User?.FindFirst(System.Security.Claims.ClaimTypes.Role)?.Value;
    }

    /// <summary>
    /// Gets the current user's factory ID from claims
    /// </summary>
    protected int? GetCurrentUserFactoryId()
    {
        var factoryIdClaim = User?.FindFirst("FactoryId")?.Value;
        return int.TryParse(factoryIdClaim, out var factoryId) ? factoryId : null;
    }

    /// <summary>
    /// Checks if the current user has the specified role
    /// </summary>
    protected bool HasRole(string role)
    {
        return User?.IsInRole(role) ?? false;
    }

    /// <summary>
    /// Checks if the current user has any of the specified roles
    /// </summary>
    protected bool HasAnyRole(params string[] roles)
    {
        return roles.Any(role => User?.IsInRole(role) ?? false);
    }
}
