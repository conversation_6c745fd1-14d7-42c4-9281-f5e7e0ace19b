# Minimal fix - just comment out problematic controller classes entirely

$controllersPath = "src\HWSAuditPlatform.ApiService\Controllers"

# Controllers that are working and should be kept
$workingControllers = @(
    "BaseController.cs",
    "UsersController.cs", 
    "AuditsController.cs",
    "AuthController.cs",
    "EvidenceTypesController.cs"
)

# Get all controller files
$controllerFiles = Get-ChildItem -Path $controllersPath -Filter "*.cs"

foreach ($file in $controllerFiles) {
    if ($file.Name -in $workingControllers) {
        Write-Host "Skipping working controller: $($file.Name)"
        continue
    }
    
    Write-Host "Disabling controller: $($file.Name)"
    
    $content = Get-Content $file.FullName -Raw
    
    # Add comment at the top and wrap entire class in comment
    $newContent = @"
// TODO: Controller temporarily disabled during MediatR removal
// This controller needs to be migrated to use service pattern
/*
$content
*/
"@
    
    Set-Content $file.FullName $newContent -NoNewline
}

Write-Host "Minimal fix completed - disabled problematic controllers"
