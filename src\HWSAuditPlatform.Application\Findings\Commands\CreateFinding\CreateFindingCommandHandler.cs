using Microsoft.EntityFrameworkCore;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Application.Findings.DTOs;
using HWSAuditPlatform.Domain.Entities.Findings;
using HWSAuditPlatform.Domain.Enums;
using HWSAuditPlatform.Domain.Common;

namespace HWSAuditPlatform.Application.Findings.Commands.CreateFinding;

/// <summary>
/// Handler for creating a new finding
/// </summary>
public class CreateFindingCommandHandler : BaseCommandHandler<CreateFindingCommand, FindingDto>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public CreateFindingCommandHandler(
        IApplicationDbContext context,
        ICurrentUserService currentUserService)
    {
        _context = context;
        _currentUserService = currentUserService;
    }

    public override async Task<FindingDto> Handle(CreateFindingCommand request, CancellationToken cancellationToken)
    {
        var currentUserId = _currentUserService.UserId;
        if (string.IsNullOrEmpty(currentUserId))
        {
            throw new UnauthorizedAccessException("User not authenticated");
        }

        // Validate that the audit answer exists and user has access
        var auditAnswer = await _context.AuditAnswers
            .Include(aa => aa.Audit)
                .ThenInclude(a => a.AuditTemplate)
            .Include(aa => aa.Audit)
                .ThenInclude(a => a.Factory)
            .Include(aa => aa.Audit)
                .ThenInclude(a => a.Area)
            .Include(aa => aa.Audit)
                .ThenInclude(a => a.SubArea)
            .FirstOrDefaultAsync(aa => aa.Id == request.AuditAnswerId, cancellationToken);

        if (auditAnswer == null)
        {
            throw new NotFoundException("AuditAnswer", request.AuditAnswerId);
        }

        // Validate finding category if provided
        if (request.FindingCategoryId.HasValue)
        {
            var category = await _context.FindingCategories
                .FirstOrDefaultAsync(fc => fc.Id == request.FindingCategoryId.Value && 
                                          fc.AuditTemplateId == auditAnswer.Audit.AuditTemplateId && 
                                          fc.IsActive, cancellationToken);

            if (category == null)
            {
                throw new ValidationException("FindingCategoryId", "Invalid or inactive finding category for this audit template.");
            }
        }

        // Create the finding
        var finding = new Finding
        {
            Id = CuidGenerator.Generate(),
            AuditAnswerId = request.AuditAnswerId,
            FindingDescription = request.FindingDescription,
            FindingSeverityLevel = request.FindingSeverityLevel,
            RootCauseAnalysis = request.RootCauseAnalysis,
            ImmediateActionTaken = request.ImmediateActionTaken,
            Status = FindingStatus.Open,
            ReportedByUserId = currentUserId,
            DueDate = request.DueDate,
            FindingCategoryId = request.FindingCategoryId
        };

        // TODO: Implement area-based responsibility assignment
        // This would assign ResponsibleUserId and RetrospectiveAnalystUserId based on area configuration

        _context.Findings.Add(finding);
        await _context.SaveChangesAsync(cancellationToken);

        // Return the created finding as DTO
        return await MapToDto(finding, auditAnswer, cancellationToken);
    }

    private async Task<FindingDto> MapToDto(Finding finding, Domain.Entities.Audits.AuditAnswer auditAnswer, CancellationToken cancellationToken)
    {
        // Get additional data for the DTO
        var reportedByUser = await _context.Users
            .FirstOrDefaultAsync(u => u.AdObjectGuid == finding.ReportedByUserId, cancellationToken);

        var responsibleUser = !string.IsNullOrEmpty(finding.ResponsibleUserId)
            ? await _context.Users.FirstOrDefaultAsync(u => u.AdObjectGuid == finding.ResponsibleUserId, cancellationToken)
            : null;

        var retrospectiveAnalyst = !string.IsNullOrEmpty(finding.RetrospectiveAnalystUserId)
            ? await _context.Users.FirstOrDefaultAsync(u => u.AdObjectGuid == finding.RetrospectiveAnalystUserId, cancellationToken)
            : null;

        var category = finding.FindingCategoryId.HasValue
            ? await _context.FindingCategories.FirstOrDefaultAsync(fc => fc.Id == finding.FindingCategoryId.Value, cancellationToken)
            : null;

        return new FindingDto
        {
            Id = finding.Id,
            AuditAnswerId = finding.AuditAnswerId,
            AuditId = auditAnswer.AuditId,
            FindingCode = finding.FindingCode,
            FindingDescription = finding.FindingDescription,
            FindingSeverityLevel = finding.FindingSeverityLevel,
            RootCauseAnalysis = finding.RootCauseAnalysis,
            ImmediateActionTaken = finding.ImmediateActionTaken,
            Status = finding.Status,
            ReportedByUserId = finding.ReportedByUserId,
            ReportedByUserName = reportedByUser?.Username,
            ReportedByUserFullName = reportedByUser?.FullName,
            DueDate = finding.DueDate,
            ResponsibleUserId = finding.ResponsibleUserId,
            ResponsibleUserName = responsibleUser?.Username,
            ResponsibleUserFullName = responsibleUser?.FullName,
            RetrospectiveAnalystUserId = finding.RetrospectiveAnalystUserId,
            RetrospectiveAnalystUserName = retrospectiveAnalyst?.Username,
            RetrospectiveAnalystUserFullName = retrospectiveAnalyst?.FullName,
            FindingCategoryId = finding.FindingCategoryId,
            FindingCategoryName = category?.CategoryName,
            FindingCategoryColorCode = category?.ColorCode,
            FindingCategoryIconName = category?.IconName,
            AuditTemplateName = auditAnswer.Audit.AuditTemplate.TemplateName,
            FactoryName = auditAnswer.Audit.Factory.FactoryName,
            AreaName = auditAnswer.Audit.Area.AreaName,
            SubAreaName = auditAnswer.Audit.SubArea?.SubAreaName,
            CorrectiveActions = new List<FindingCorrectiveActionSummaryDto>(), // Empty for new finding
            IsOverdue = finding.IsOverdue,
            IsOpen = finding.IsOpen,
            IsClosed = finding.IsClosed,
            CorrectiveActionCount = 0,
            OpenCorrectiveActionCount = 0,
            CreatedAt = finding.CreatedAt,
            UpdatedAt = finding.UpdatedAt,
            CreatedByUserId = finding.CreatedByUserId,
            UpdatedByUserId = finding.UpdatedByUserId
        };
    }
}
