using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Users.DTOs;

using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Application.Users.Services;

/// <summary>
/// Service interface for user management operations
/// </summary>
public interface IUserService : IBaseService
{
    /// <summary>
    /// Gets a paginated list of users
    /// </summary>
    Task<PaginatedResult<UserSummaryDto>> GetUsersAsync(GetUsersRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets a single user by ID
    /// </summary>
    Task<UserDto> GetUserAsync(string id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Creates a new user
    /// </summary>
    Task<string> CreateUserAsync(CreateUserRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates an existing user
    /// </summary>
    Task UpdateUserAsync(UpdateUserRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Deletes a user
    /// </summary>
    Task DeleteUserAsync(string id, CancellationToken cancellationToken = default);



    /// <summary>
    /// Gets all roles
    /// </summary>
    Task<List<RoleDto>> GetRolesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets user groups with pagination
    /// </summary>
    Task<PaginatedResult<UserGroupSummaryDto>> GetUserGroupsAsync(GetUserGroupsRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets a single user group by ID
    /// </summary>
    Task<UserGroupDto> GetUserGroupAsync(int id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Creates a new user group
    /// </summary>
    Task<int> CreateUserGroupAsync(CreateUserGroupRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates an existing user group
    /// </summary>
    Task UpdateUserGroupAsync(UpdateUserGroupRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Deletes a user group
    /// </summary>
    Task DeleteUserGroupAsync(int id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets AD group role mappings
    /// </summary>
    Task<List<AdGroupRoleMappingDto>> GetAdGroupRoleMappingsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Creates a new AD group role mapping
    /// </summary>
    Task<int> CreateAdGroupRoleMappingAsync(CreateAdGroupRoleMappingRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates an existing AD group role mapping
    /// </summary>
    Task UpdateAdGroupRoleMappingAsync(UpdateAdGroupRoleMappingRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Deletes an AD group role mapping
    /// </summary>
    Task DeleteAdGroupRoleMappingAsync(int id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets process owner assignments with pagination
    /// </summary>
    Task<PaginatedResult<ProcessOwnerAssignmentDto>> GetProcessOwnerAssignmentsAsync(GetProcessOwnerAssignmentsRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Creates a new process owner assignment
    /// </summary>
    Task<int> CreateProcessOwnerAssignmentAsync(CreateProcessOwnerAssignmentRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates an existing process owner assignment
    /// </summary>
    Task UpdateProcessOwnerAssignmentAsync(UpdateProcessOwnerAssignmentRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Deletes a process owner assignment
    /// </summary>
    Task DeleteProcessOwnerAssignmentAsync(int id, CancellationToken cancellationToken = default);

    /// <summary>
    /// Searches for users with fuzzy matching
    /// </summary>
    Task<List<UserSummaryDto>> SearchUsersAsync(string searchTerm, int maxResults = 5, CancellationToken cancellationToken = default);
}

/// <summary>
/// Request model for getting users
/// </summary>
public class GetUsersRequest : BaseQuery<PaginatedResult<UserSummaryDto>>
{
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public string? SearchTerm { get; set; }
    public UserRole? Role { get; set; }
    public bool? IsActive { get; set; }
    public int? FactoryId { get; set; }
    public string? SortBy { get; set; } = "Username";
    public bool SortDescending { get; set; } = false;
}

/// <summary>
/// Request model for creating a user
/// </summary>
public class CreateUserRequest : BaseRequest<string>
{
    public string Username { get; set; } = string.Empty;
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string? Email { get; set; }
    public UserRole Role { get; set; }
    public int? FactoryId { get; set; }
    public bool IsActive { get; set; } = true;
}

/// <summary>
/// Request model for updating a user
/// </summary>
public class UpdateUserRequest : BaseRequest
{
    public string Id { get; set; } = string.Empty;
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string? Email { get; set; }
    public UserRole Role { get; set; }
    public int? FactoryId { get; set; }
    public bool IsActive { get; set; }
    public int RecordVersion { get; set; }
}



/// <summary>
/// Request model for getting user groups
/// </summary>
public class GetUserGroupsRequest : BaseQuery<PaginatedResult<UserGroupSummaryDto>>
{
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public string? SearchTerm { get; set; }
    public bool? IsActive { get; set; }
    public string? SortBy { get; set; } = "GroupName";
    public bool SortDescending { get; set; } = false;
}

/// <summary>
/// Request model for creating a user group
/// </summary>
public class CreateUserGroupRequest : BaseRequest<int>
{
    public string GroupName { get; set; } = string.Empty;
    public string? Description { get; set; }
    public List<string> MemberIds { get; set; } = new();
}

/// <summary>
/// Request model for updating a user group
/// </summary>
public class UpdateUserGroupRequest : BaseRequest
{
    public int Id { get; set; }
    public string GroupName { get; set; } = string.Empty;
    public string? Description { get; set; }
    public List<string> MemberIds { get; set; } = new();
    public int RecordVersion { get; set; }
}

/// <summary>
/// Request model for creating AD group role mapping
/// </summary>
public class CreateAdGroupRoleMappingRequest : BaseRequest<int>
{
    public string AdGroupName { get; set; } = string.Empty;
    public int RoleId { get; set; }
}

/// <summary>
/// Request model for updating AD group role mapping
/// </summary>
public class UpdateAdGroupRoleMappingRequest : BaseRequest
{
    public int Id { get; set; }
    public string AdGroupName { get; set; } = string.Empty;
    public int RoleId { get; set; }
    public int RecordVersion { get; set; }
}

/// <summary>
/// Request model for getting process owner assignments
/// </summary>
public class GetProcessOwnerAssignmentsRequest : BaseQuery<PaginatedResult<ProcessOwnerAssignmentDto>>
{
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public string? ProcessOwnerUserId { get; set; }
    public int? FactoryId { get; set; }
    public int? AreaId { get; set; }
    public bool? IsActive { get; set; }
    public string? SortBy { get; set; } = "ProcessOwner";
    public bool SortDescending { get; set; } = false;
}

/// <summary>
/// Request model for creating process owner assignment
/// </summary>
public class CreateProcessOwnerAssignmentRequest : BaseRequest<int>
{
    public string ProcessOwnerUserId { get; set; } = string.Empty;
    public int? FactoryId { get; set; }
    public int? AreaId { get; set; }
    public int? SubAreaId { get; set; }
    public string? Description { get; set; }
}

/// <summary>
/// Request model for updating process owner assignment
/// </summary>
public class UpdateProcessOwnerAssignmentRequest : BaseRequest
{
    public int Id { get; set; }
    public string ProcessOwnerUserId { get; set; } = string.Empty;
    public int? FactoryId { get; set; }
    public int? AreaId { get; set; }
    public int? SubAreaId { get; set; }
    public string? Description { get; set; }
    public bool IsActive { get; set; } = true;
    public int RecordVersion { get; set; }
}
