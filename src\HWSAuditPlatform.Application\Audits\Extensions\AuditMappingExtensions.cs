using HWSAuditPlatform.Application.Audits.DTOs;
using HWSAuditPlatform.Application.Templates.DTOs;
using HWSAuditPlatform.Domain.Entities.Audits;
using HWSAuditPlatform.Domain.Entities.Templates;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Application.Audits.Extensions;

/// <summary>
/// Extension methods for mapping Audit entities to DTOs
/// </summary>
public static class AuditMappingExtensions
{
    /// <summary>
    /// Maps Audit entity to AuditDto
    /// </summary>
    public static AuditDto ToDto(this Audit audit)
    {
        return new AuditDto
        {
            Id = audit.Id,
            AuditTemplateId = audit.AuditTemplateId,
            AuditTemplateName = audit.AuditTemplate?.TemplateName,
            AssignmentType = audit.AssignmentType,
            AssignedToUserGroupId = audit.AssignedToUserGroupId,
            AssignedToUserGroupName = audit.AssignedToUserGroup?.GroupName,
            AssignedToUserId = audit.AssignedToUserId,
            AssignedToUserName = audit.AssignedToUser != null 
                ? $"{audit.AssignedToUser.FirstName} {audit.AssignedToUser.LastName}".Trim() 
                : null,
            ScheduledDate = audit.ScheduledDate,
            DueDate = audit.DueDate,
            StartedAt = audit.StartedAt,
            CompletedAt = audit.CompletedAt,
            OverallStatus = audit.OverallStatus,
            FactoryId = audit.FactoryId,
            FactoryName = audit.Factory?.FactoryName,
            AreaId = audit.AreaId,
            AreaName = audit.Area?.AreaName,
            SubAreaId = audit.SubAreaId,
            SubAreaName = audit.SubArea?.SubAreaName,
            OverallScore = audit.OverallScore,
            ManagerComments = audit.ManagerComments,
            ReviewedByUserId = audit.ReviewedByUserId,
            ReviewedByUserName = audit.ReviewedByUser != null 
                ? $"{audit.ReviewedByUser.FirstName} {audit.ReviewedByUser.LastName}".Trim() 
                : null,
            ReviewedAt = audit.ReviewedAt,
            RecurringAuditSettingId = audit.RecurringAuditSettingId,
            CreatedAt = audit.CreatedAt,
            CreatedByUserId = audit.CreatedByUserId,
            UpdatedAt = audit.UpdatedAt,
            UpdatedByUserId = audit.UpdatedByUserId,
            RecordVersion = audit.RecordVersion,
            QuestionGroups = audit.AuditTemplate?.QuestionGroups?.Where(g => g.IsActive).Select(g => g.ToDto()).ToList() ?? new(),
            Questions = audit.AuditTemplate?.Questions?.Where(q => q.IsActive).Select(q => q.ToDto()).ToList() ?? new()
        };
    }

    /// <summary>
    /// Maps Audit entity to AuditSummaryDto
    /// </summary>
    public static AuditSummaryDto ToSummaryDto(this Audit audit)
    {
        return new AuditSummaryDto
        {
            Id = audit.Id,
            AuditTemplateName = audit.AuditTemplate?.TemplateName,
            AssignedToUserName = audit.AssignedToUser != null 
                ? $"{audit.AssignedToUser.FirstName} {audit.AssignedToUser.LastName}".Trim() 
                : null,
            ScheduledDate = audit.ScheduledDate,
            DueDate = audit.DueDate,
            OverallStatus = audit.OverallStatus,
            FactoryName = audit.Factory?.FactoryName,
            AreaName = audit.Area?.AreaName,
            OverallScore = audit.OverallScore,
            IsOverdue = audit.DueDate.HasValue && audit.DueDate.Value < DateTime.UtcNow && 
                audit.OverallStatus != AuditOverallStatus.Closed && 
                audit.OverallStatus != AuditOverallStatus.Cancelled
        };
    }

    /// <summary>
    /// Maps Audit entity to AuditForReviewDto
    /// </summary>
    public static AuditForReviewDto ToForReviewDto(this Audit audit)
    {
        return new AuditForReviewDto
        {
            Id = audit.Id,
            AuditTemplateId = audit.AuditTemplateId,
            AuditTemplateName = audit.AuditTemplate?.TemplateName,
            AssignedToUserId = audit.AssignedToUserId,
            AssignedToUserName = audit.AssignedToUser != null 
                ? $"{audit.AssignedToUser.FirstName} {audit.AssignedToUser.LastName}".Trim() 
                : null,
            ScheduledDate = audit.ScheduledDate,
            DueDate = audit.DueDate,
            StartedAt = audit.StartedAt,
            CompletedAt = audit.CompletedAt,
            OverallStatus = audit.OverallStatus,
            FactoryId = audit.FactoryId,
            FactoryName = audit.Factory?.FactoryName,
            AreaId = audit.AreaId,
            AreaName = audit.Area?.AreaName,
            SubAreaId = audit.SubAreaId,
            SubAreaName = audit.SubArea?.SubAreaName,
            OverallScore = audit.OverallScore,
            ManagerComments = audit.ManagerComments,
            ReviewedByUserId = audit.ReviewedByUserId,
            ReviewedByUserName = audit.ReviewedByUser != null 
                ? $"{audit.ReviewedByUser.FirstName} {audit.ReviewedByUser.LastName}".Trim() 
                : null,
            ReviewedAt = audit.ReviewedAt,
            IsOverdue = audit.DueDate.HasValue && audit.DueDate.Value < DateTime.UtcNow && 
                audit.OverallStatus != AuditOverallStatus.Closed && 
                audit.OverallStatus != AuditOverallStatus.Cancelled,
            Answers = new(), // Will be populated separately
            TotalQuestions = 0, // Will be calculated
            AnsweredQuestions = 0, // Will be calculated
            PassedQuestions = 0, // Will be calculated
            FailedQuestions = 0, // Will be calculated
            NotApplicableQuestions = 0, // Will be calculated
            QuestionsWithFindings = 0, // Will be calculated
            TotalAttachments = 0 // Will be calculated
        };
    }

    /// <summary>
    /// Maps AuditAnswer entity to AuditAnswerDto
    /// </summary>
    public static AuditAnswerDto ToDto(this AuditAnswer auditAnswer)
    {
        return new AuditAnswerDto
        {
            Id = auditAnswer.Id,
            AuditId = auditAnswer.AuditId,
            QuestionId = auditAnswer.QuestionId,
            QuestionText = auditAnswer.Question?.QuestionText,
            QuestionType = auditAnswer.Question?.QuestionType ?? QuestionType.YesNo,
            AnswerValue = auditAnswer.AnswerValue, // This uses the computed property
            IsNotApplicable = auditAnswer.IsNotApplicable,
            Comments = auditAnswer.Comments,
            SeverityLevel = auditAnswer.SeverityLevel,
            CreatedAt = auditAnswer.CreatedAt,
            CreatedByUserId = auditAnswer.CreatedByUserId,
            UpdatedAt = auditAnswer.UpdatedAt,
            UpdatedByUserId = auditAnswer.UpdatedByUserId,
            SelectedOptions = auditAnswer.SelectedOptions?.Select(so => new AuditAnswerSelectedOptionDto
            {
                Id = so.Id,
                AuditAnswerId = so.AuditAnswerId,
                QuestionOptionId = so.QuestionOptionId,
                OptionText = so.QuestionOption?.OptionText ?? string.Empty
            }).ToList() ?? new(),
            FailureReasons = auditAnswer.FailureReasons?.Select(fr => new AuditAnswerFailureReasonDto
            {
                Id = fr.Id,
                AuditAnswerId = fr.AuditAnswerId,
                FailureReasonText = fr.ReasonText
            }).ToList() ?? new(),
            Attachments = auditAnswer.Attachments?.Select(a => new AuditAttachmentDto
            {
                Id = a.Id,
                AuditAnswerId = a.AuditAnswerId,
                FileName = a.FileName ?? string.Empty,
                OriginalFileName = a.OriginalFileName,
                ContentType = a.ContentType,
                FileSize = a.FileSize ?? 0,
                FilePath = a.FilePath,
                Description = a.Description,
                CreatedAt = a.CreatedAt,
                CreatedByUserId = a.CreatedByUserId
            }).ToList() ?? new()
        };
    }

    /// <summary>
    /// Maps collection of Audit entities to DTOs
    /// </summary>
    public static List<AuditDto> ToDtos(this IEnumerable<Audit> audits)
    {
        return audits.Select(a => a.ToDto()).ToList();
    }

    /// <summary>
    /// Maps collection of Audit entities to summary DTOs
    /// </summary>
    public static List<AuditSummaryDto> ToSummaryDtos(this IEnumerable<Audit> audits)
    {
        return audits.Select(a => a.ToSummaryDto()).ToList();
    }

    /// <summary>
    /// Maps collection of Audit entities to review DTOs
    /// </summary>
    public static List<AuditForReviewDto> ToForReviewDtos(this IEnumerable<Audit> audits)
    {
        return audits.Select(a => a.ToForReviewDto()).ToList();
    }

    /// <summary>
    /// Maps collection of AuditAnswer entities to DTOs
    /// </summary>
    public static List<AuditAnswerDto> ToDtos(this IEnumerable<AuditAnswer> auditAnswers)
    {
        return auditAnswers.Select(aa => aa.ToDto()).ToList();
    }
}

/// <summary>
/// Extension methods for mapping Question entities to DTOs (needed for audit templates)
/// </summary>
public static class QuestionMappingExtensions
{
    /// <summary>
    /// Maps Question entity to QuestionDto
    /// </summary>
    public static QuestionDto ToDto(this Question question)
    {
        return new QuestionDto
        {
            Id = question.Id,
            QuestionText = question.QuestionText,
            QuestionType = question.QuestionType,
            IsRequired = question.IsRequired,
            DisplayOrder = question.DisplayOrder,
            IsActive = question.IsActive,
            QuestionGroupId = question.QuestionGroupId,
            AuditTemplateId = question.AuditTemplateId,
            CreatedAt = question.CreatedAt,
            CreatedByUserId = question.CreatedByUserId,
            UpdatedAt = question.UpdatedAt,
            UpdatedByUserId = question.UpdatedByUserId,
            RecordVersion = question.RecordVersion,
            Options = question.Options?.Where(o => o.IsActive).Select(o => o.ToDto()).ToList() ?? new(),
            AllowedEvidenceTypes = question.AllowedEvidenceTypes?.Where(aet => aet.IsActive).Select(aet => aet.EvidenceType).ToList() ?? new()
        };
    }

    /// <summary>
    /// Maps QuestionOption entity to QuestionOptionDto
    /// </summary>
    public static QuestionOptionDto ToDto(this QuestionOption option)
    {
        return new QuestionOptionDto
        {
            Id = option.Id,
            OptionText = option.OptionText,
            DisplayOrder = option.DisplayOrder,
            IsActive = option.IsActive,
            QuestionId = option.QuestionId
        };
    }
}

/// <summary>
/// Extension methods for mapping QuestionGroup entities to DTOs
/// </summary>
public static class QuestionGroupMappingExtensions
{
    /// <summary>
    /// Maps QuestionGroup entity to QuestionGroupDto
    /// </summary>
    public static QuestionGroupDto ToDto(this QuestionGroup questionGroup)
    {
        return new QuestionGroupDto
        {
            Id = questionGroup.Id,
            GroupName = questionGroup.GroupName,
            DisplayOrder = questionGroup.DisplayOrder,
            IsActive = questionGroup.IsActive,
            AuditTemplateId = questionGroup.AuditTemplateId,
            Questions = questionGroup.Questions?.Where(q => q.IsActive).Select(q => q.ToDto()).ToList() ?? new()
        };
    }
}
