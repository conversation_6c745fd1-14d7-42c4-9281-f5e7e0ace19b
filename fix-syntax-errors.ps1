# PowerShell script to fix syntax errors by commenting out problematic method bodies

$controllersPath = "src\HWSAuditPlatform.ApiService\Controllers"

# Get all controller files that have syntax errors
$errorFiles = @(
    "AreaResponsibilityController.cs",
    "AuditsController.cs", 
    "CorrectiveActionsController.cs",
    "FindingCategoriesController.cs",
    "FindingsController.cs",
    "OrganizationController.cs",
    "ProcessOwnerAssignmentsController.cs",
    "RecurringAuditsController.cs",
    "TemplateAccessController.cs",
    "TemplatesController.cs",
    "UsersControllerNew.cs"
)

foreach ($fileName in $errorFiles) {
    $filePath = Join-Path $controllersPath $fileName
    if (Test-Path $filePath) {
        Write-Host "Processing $fileName..."
        
        $content = Get-Content $filePath -Raw
        
        # Fix the broken parameter syntax by replacing the malformed patterns
        $content = $content -replace '\[FromBody\] /\* // TODO: Replace with service call \*/ var\)', '[FromBody] object request /* TODO: Fix parameter type */)'
        $content = $content -replace '/\* // TODO: Replace with service call \*/ var ', 'var /* TODO: Fix variable type */ '
        
        # Replace any remaining problematic patterns
        $content = $content -replace '/\* // TODO: Replace with service call \*/ null', 'null /* TODO: Replace with service call */'
        
        Set-Content $filePath $content -NoNewline
        Write-Host "Fixed syntax in $fileName"
    }
}

Write-Host "Syntax fixes completed!"
