using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Application.Users.DTOs;
using HWSAuditPlatform.Domain.Entities.Users;
using HWSAuditPlatform.Domain.Enums;
using Microsoft.EntityFrameworkCore;

namespace HWSAuditPlatform.Application.Users.Queries.GetProcessOwnerAssignments;

/// <summary>
/// Handler for GetProcessOwnerAssignmentsQuery
/// </summary>
public class GetProcessOwnerAssignmentsQueryHandler
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public GetProcessOwnerAssignmentsQueryHandler(
        IApplicationDbContext context,
        ICurrentUserService currentUserService)
    {
        _context = context;
        _currentUserService = currentUserService;
    }

    public async Task<PaginatedResult<ProcessOwnerAssignmentDto>> Handle(GetProcessOwnerAssignmentsQuery request, CancellationToken cancellationToken)
    {
        var currentUserId = _currentUserService.UserId;
        if (string.IsNullOrEmpty(currentUserId))
        {
            throw new UnauthorizedAccessException("User not authenticated");
        }

        // Verify current user has permission to view assignments
        var currentUser = await _context.Users
            .Include(u => u.Role)
            .FirstOrDefaultAsync(u => u.AdObjectGuid == currentUserId, cancellationToken);

        if (currentUser?.Role.RoleName != UserRole.SystemManager && 
            currentUser?.Role.RoleName != UserRole.DevAdmin &&
            currentUser?.Role.RoleName != UserRole.ProcessOwner)
        {
            throw new UnauthorizedAccessException("Insufficient permissions to view process owner assignments");
        }

        var query = _context.ProcessOwnerAssignments
            .Include(poa => poa.ProcessOwnerUser)
            .Include(poa => poa.AssignedByUser)
            .Include(poa => poa.Factory)
            .Include(poa => poa.Area)
            .Include(poa => poa.SubArea)
            .AsQueryable();

        // Apply filters
        if (!string.IsNullOrEmpty(request.ProcessOwnerUserId))
        {
            query = query.Where(poa => poa.ProcessOwnerUserId == request.ProcessOwnerUserId);
        }

        if (request.FactoryId.HasValue)
        {
            query = query.Where(poa => poa.FactoryId == request.FactoryId);
        }

        if (request.AreaId.HasValue)
        {
            query = query.Where(poa => poa.AreaId == request.AreaId);
        }

        if (request.SubAreaId.HasValue)
        {
            query = query.Where(poa => poa.SubAreaId == request.SubAreaId);
        }

        if (request.IsActive.HasValue)
        {
            query = query.Where(poa => poa.IsActive == request.IsActive);
        }

        // If current user is ProcessOwner, only show their own assignments
        if (currentUser.Role.RoleName == UserRole.ProcessOwner)
        {
            query = query.Where(poa => poa.ProcessOwnerUserId == currentUserId);
        }

        // Apply sorting
        if (!string.IsNullOrEmpty(request.SortBy))
        {
            query = request.SortBy.ToLower() switch
            {
                "processownerusername" => request.SortDescending 
                    ? query.OrderByDescending(poa => poa.ProcessOwnerUser.Username)
                    : query.OrderBy(poa => poa.ProcessOwnerUser.Username),
                "factoryname" => request.SortDescending 
                    ? query.OrderByDescending(poa => poa.Factory!.FactoryName)
                    : query.OrderBy(poa => poa.Factory!.FactoryName),
                "areaname" => request.SortDescending 
                    ? query.OrderByDescending(poa => poa.Area!.AreaName)
                    : query.OrderBy(poa => poa.Area!.AreaName),
                "createdat" => request.SortDescending 
                    ? query.OrderByDescending(poa => poa.CreatedAt)
                    : query.OrderBy(poa => poa.CreatedAt),
                _ => query.OrderByDescending(poa => poa.CreatedAt)
            };
        }
        else
        {
            query = query.OrderByDescending(poa => poa.CreatedAt);
        }

        var totalCount = await query.CountAsync(cancellationToken);
        var items = await query
            .Skip((request.PageNumber - 1) * request.PageSize)
            .Take(request.PageSize)
            .ToListAsync(cancellationToken);

        var dtos = items.Select(MapToDto).ToList();

        return PaginatedResult<ProcessOwnerAssignmentDto>.Create(dtos, totalCount, request.PageNumber, request.PageSize);
    }

    private ProcessOwnerAssignmentDto MapToDto(ProcessOwnerAssignment assignment)
    {
        return new ProcessOwnerAssignmentDto
        {
            Id = assignment.Id,
            ProcessOwnerUserId = assignment.ProcessOwnerUserId,
            ProcessOwnerUserName = assignment.ProcessOwnerUser.Username,
            ProcessOwnerFullName = assignment.ProcessOwnerUser.FullName,
            FactoryId = assignment.FactoryId,
            FactoryName = assignment.Factory?.FactoryName,
            AreaId = assignment.AreaId,
            AreaName = assignment.Area?.AreaName,
            SubAreaId = assignment.SubAreaId,
            SubAreaName = assignment.SubArea?.SubAreaName,
            AssignedByUserId = assignment.AssignedByUserId,
            AssignedByUserName = assignment.AssignedByUser.Username,
            AssignedByFullName = assignment.AssignedByUser.FullName,
            IsActive = assignment.IsActive,
            Description = assignment.Description,
            ScopeDescription = assignment.GetScopeDescription(),
            CreatedAt = assignment.CreatedAt,
            UpdatedAt = assignment.UpdatedAt,
            CreatedByUserId = assignment.CreatedByUserId,
            UpdatedByUserId = assignment.UpdatedByUserId
        };
    }
}
