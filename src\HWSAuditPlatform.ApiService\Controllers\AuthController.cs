using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using Asp.Versioning;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.IdentityModel.Tokens;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Application.Users.DTOs;
using HWSAuditPlatform.Application.Users.Services;
// using HWSAuditPlatform.Application.Users.Commands.CreateUser; // Removed - using service pattern
// using HWSAuditPlatform.Application.Users.Queries.GetUser; // Removed - using service pattern
using HWSAuditPlatform.ApiService.Models;

namespace HWSAuditPlatform.ApiService.Controllers;

/// <summary>
/// Controller for authentication operations
/// </summary>
[Route("api/v{version:apiVersion}/[controller]")]
[ApiVersion("1.0")]
public class AuthController : BaseController
{
    private readonly IActiveDirectoryService _activeDirectoryService;
    private readonly IConfiguration _configuration;
    private readonly IAdRoleMappingService _adRoleMappingService;
    private readonly IAuditLogService _auditLogService;

    public AuthController(
        ILogger<AuthController> logger,
        IActiveDirectoryService activeDirectoryService,
        IConfiguration configuration,
        IAdRoleMappingService adRoleMappingService,
        IAuditLogService auditLogService)
        : base(logger)
    {
        _activeDirectoryService = activeDirectoryService;
        _configuration = configuration;
        _adRoleMappingService = adRoleMappingService;
        _auditLogService = auditLogService;
    }

    /// <summary>
    /// Authenticate user and return JWT token
    /// </summary>
    /// <param name="request">Login request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Authentication result with JWT token</returns>
    [HttpPost("login")]
    [AllowAnonymous]
    [ProducesResponseType(typeof(AuthenticationResult), 200)]
    [ProducesResponseType(typeof(ApiErrorResponse), 401)]
    public async Task<ActionResult<AuthenticationResult>> Login(
        LoginRequest request,
        CancellationToken cancellationToken)
    {
        if (string.IsNullOrEmpty(request.Username) || string.IsNullOrEmpty(request.Password))
        {
            Logger.LogWarning("Login attempt with missing username or password");
            return Unauthorized(new ApiErrorResponse
            {
                StatusCode = 401,
                Title = "Authentication Failed",
                Detail = "Username and password are required"
            });
        }

        try
        {
            Logger.LogInformation("Login attempt for username: {Username}", request.Username);

            // Validate credentials against Active Directory
            var isValidCredentials = await _activeDirectoryService.ValidateCredentialsAsync(
                request.Username, 
                request.Password, 
                cancellationToken);

            if (!isValidCredentials)
            {
                Logger.LogWarning("Invalid credentials for username: {Username}", request.Username);

                // Log failed authentication event
                try
                {
                    await _auditLogService.LogAuthenticationEventAsync(
                        "LoginFailure",
                        request.Username,
                        "Invalid credentials provided",
                        cancellationToken: cancellationToken);
                }
                catch (Exception auditEx)
                {
                    Logger.LogWarning(auditEx, "Failed to log authentication event for user {Username}", request.Username);
                }

                return Unauthorized(new ApiErrorResponse
                {
                    StatusCode = 401,
                    Title = "Authentication Failed",
                    Detail = "Invalid username or password"
                });
            }

            // Get user information from Active Directory
            var adUser = await _activeDirectoryService.GetUserByUsernameAsync(request.Username, cancellationToken);
            if (adUser == null)
            {
                Logger.LogWarning("User not found in Active Directory: {Username}", request.Username);

                // Log failed authentication event
                try
                {
                    await _auditLogService.LogAuthenticationEventAsync(
                        "LoginFailure",
                        request.Username,
                        "User not found in Active Directory",
                        cancellationToken: cancellationToken);
                }
                catch (Exception auditEx)
                {
                    Logger.LogWarning(auditEx, "Failed to log authentication event for user {Username}", request.Username);
                }

                return Unauthorized(new ApiErrorResponse
                {
                    StatusCode = 401,
                    Title = "Authentication Failed",
                    Detail = "User not found"
                });
            }

            // Get user role by first checking database, then falling back to AD group mapping
            var (userRole, userExistsInDb) = await _adRoleMappingService.GetUserRoleWithExistenceAsync(adUser.ObjectGuid, adUser.MemberOf, cancellationToken);

            // Optionally create user in database if they don't exist (for better role management)
            if (!userExistsInDb)
            {
                Logger.LogInformation("User {Username} not found in database, creating user record with role {Role}",
                    adUser.Username, userRole);

                // TODO: Implement user creation service method
                Logger.LogWarning("User creation temporarily disabled - needs service implementation for {Username}", adUser.Username);
            }

            var factoryId = "1"; // This should come from the database

            // Generate JWT token
            var token = GenerateJwtToken(adUser.ObjectGuid, adUser.Username, adUser.Email, userRole.ToString(), factoryId);

            var result = new AuthenticationResult
            {
                Token = token,
                Username = adUser.Username,
                Email = adUser.Email,
                FullName = $"{adUser.FirstName} {adUser.LastName}".Trim(),
                Role = userRole.ToString(),
                FactoryId = factoryId,
                ExpiresAt = DateTime.UtcNow.AddHours(8) // Token expires in 8 hours
            };

            Logger.LogInformation("Successful login for username: {Username}", request.Username);

            // Log authentication event
            try
            {
                await _auditLogService.LogAuthenticationEventAsync(
                    "LoginSuccess",
                    adUser.Username,
                    $"Successful login with role {userRole}",
                    cancellationToken: cancellationToken);
            }
            catch (Exception auditEx)
            {
                Logger.LogWarning(auditEx, "Failed to log authentication event for user {Username}", request.Username);
            }

            return Success(result, "Login successful");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error during login for username: {Username}", request.Username);
            return StatusCode(500, new ApiErrorResponse
            {
                StatusCode = 500,
                Title = "Authentication Error",
                Detail = "An error occurred during authentication"
            });
        }
    }

    /// <summary>
    /// Test login endpoint that bypasses Active Directory (for development/testing only)
    /// </summary>
    /// <param name="request">Login request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Authentication result with JWT token</returns>
    [HttpPost("test-login")]
    [AllowAnonymous]
    [ProducesResponseType(typeof(AuthenticationResult), 200)]
    [ProducesResponseType(typeof(ApiErrorResponse), 401)]
    public async Task<ActionResult<AuthenticationResult>> TestLogin(
        LoginRequest request,
        CancellationToken cancellationToken)
    {
        if (string.IsNullOrEmpty(request.Username) || string.IsNullOrEmpty(request.Password))
        {
            Logger.LogWarning("Test login attempt with missing username or password");
            return Unauthorized(new ApiErrorResponse
            {
                StatusCode = 401,
                Title = "Authentication Failed",
                Detail = "Username and password are required"
            });
        }

        try
        {
            Logger.LogInformation("Test login attempt for username: {Username}", request.Username);

            // Test credentials - bypass AD for development
            var validTestUsers = new Dictionary<string, (string password, string role, string email, string fullName)>
            {
                { "admin.user", ("HWSAudit123!", "DevAdmin", "<EMAIL>", "Admin User") },
                { "devadmin.user", ("HWSAudit123!", "DevAdmin", "<EMAIL>", "Dev Admin User") },
                { "systemmanager.user", ("HWSAudit123!", "SystemManager", "<EMAIL>", "System Manager User") },
                { "processowner.user", ("HWSAudit123!", "ProcessOwner", "<EMAIL>", "Process Owner User") },
                { "auditor.user", ("HWSAudit123!", "Auditor", "<EMAIL>", "Auditor User") },
                { "viewer.user", ("HWSAudit123!", "Viewer", "<EMAIL>", "Viewer User") }
            };

            if (!validTestUsers.TryGetValue(request.Username, out var userInfo) || userInfo.password != request.Password)
            {
                Logger.LogWarning("Invalid test credentials for username: {Username}", request.Username);
                return Unauthorized(new ApiErrorResponse
                {
                    StatusCode = 401,
                    Title = "Authentication Failed",
                    Detail = "Invalid username or password"
                });
            }

            // Generate test user GUID
            var testUserId = Guid.NewGuid().ToString();
            var factoryId = "1"; // Default factory for testing

            // Generate JWT token
            var token = GenerateJwtToken(testUserId, request.Username, userInfo.email, userInfo.role, factoryId);

            var result = new AuthenticationResult
            {
                Token = token,
                Username = request.Username,
                Email = userInfo.email,
                FullName = userInfo.fullName,
                Role = userInfo.role,
                FactoryId = factoryId,
                ExpiresAt = DateTime.UtcNow.AddHours(8) // Token expires in 8 hours
            };

            Logger.LogInformation("Successful test login for username: {Username}", request.Username);

            // Log test authentication event
            try
            {
                await _auditLogService.LogAuthenticationEventAsync(
                    "TestLoginSuccess",
                    request.Username,
                    $"Successful test login with role {userInfo.role}",
                    cancellationToken: cancellationToken);
            }
            catch (Exception auditEx)
            {
                Logger.LogWarning(auditEx, "Failed to log authentication event for user {Username}", request.Username);
            }

            return Success(result, "Test login successful");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error during test login for username: {Username}", request.Username);
            return StatusCode(500, new ApiErrorResponse
            {
                StatusCode = 500,
                Title = "Authentication Error",
                Detail = "An error occurred during authentication"
            });
        }
    }

    /// <summary>
    /// Refresh JWT token
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>New JWT token</returns>
    [HttpPost("refresh")]
    [Authorize]
    [ProducesResponseType(typeof(AuthenticationResult), 200)]
    [ProducesResponseType(typeof(ApiErrorResponse), 401)]
    public async Task<ActionResult<AuthenticationResult>> RefreshToken(CancellationToken cancellationToken)
    {
        var currentUserId = GetCurrentUserId();
        var currentUsername = GetCurrentUsername();

        if (string.IsNullOrEmpty(currentUserId) || string.IsNullOrEmpty(currentUsername))
        {
            Logger.LogWarning("Token refresh attempted without valid user context");
            return Unauthorized(new ApiErrorResponse
            {
                StatusCode = 401,
                Title = "Authentication Failed",
                Detail = "Invalid user context for token refresh"
            });
        }

        try
        {
            Logger.LogInformation("Token refresh for user: {Username} (ID: {UserId})", currentUsername, currentUserId);

            // Get updated user information from database
            UserDto? userFromDb = null;
            // TODO: Implement user service method for getting user by ID
            Logger.LogWarning("User retrieval temporarily disabled - needs service implementation for {UserId}", currentUserId);

            // Use database information if available, otherwise fall back to token claims
            string userEmail;
            string fullName;
            string userRole;
            string? factoryId;

            if (userFromDb != null)
            {
                userEmail = userFromDb.Email ?? "";
                fullName = $"{userFromDb.FirstName} {userFromDb.LastName}".Trim();
                userRole = userFromDb.Role.ToString();
                factoryId = userFromDb.FactoryId?.ToString();

                Logger.LogInformation("Using updated user information from database for token refresh");
            }
            else
            {
                // Fall back to existing token claims
                userEmail = User?.FindFirst(ClaimTypes.Email)?.Value ?? "";
                fullName = $"{User?.FindFirst(ClaimTypes.GivenName)?.Value} {User?.FindFirst(ClaimTypes.Surname)?.Value}".Trim();
                userRole = GetCurrentUserRole() ?? "Auditor";
                factoryId = GetCurrentUserFactoryId()?.ToString();

                Logger.LogInformation("Using existing token claims for token refresh");
            }

            // Generate new JWT token with updated information
            var token = GenerateJwtToken(currentUserId, currentUsername, userEmail, userRole, factoryId ?? "");

            var result = new AuthenticationResult
            {
                Token = token,
                Username = currentUsername,
                Email = userEmail,
                FullName = fullName,
                Role = userRole,
                FactoryId = factoryId,
                ExpiresAt = DateTime.UtcNow.AddHours(8)
            };

            Logger.LogInformation("Token refreshed successfully for user: {Username}", currentUsername);

            // Log token refresh event
            try
            {
                await _auditLogService.LogAuthenticationEventAsync(
                    "TokenRefresh",
                    currentUsername,
                    $"JWT token refreshed successfully with role {userRole}",
                    cancellationToken: cancellationToken);
            }
            catch (Exception auditEx)
            {
                Logger.LogWarning(auditEx, "Failed to log authentication event for user {Username}", currentUsername);
            }

            return Success(result, "Token refreshed successfully");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error during token refresh for user: {Username}", currentUsername);
            return StatusCode(500, new ApiErrorResponse
            {
                StatusCode = 500,
                Title = "Token Refresh Error",
                Detail = "An error occurred during token refresh"
            });
        }
    }

    /// <summary>
    /// Logout user (client-side token invalidation)
    /// </summary>
    /// <returns>Success response</returns>
    [HttpPost("logout")]
    [Authorize]
    [ProducesResponseType(200)]
    public ActionResult Logout()
    {
        var currentUsername = GetCurrentUsername();
        Logger.LogInformation("Logout for user: {Username}", currentUsername);
        
        // In a stateless JWT implementation, logout is handled client-side
        // The client should remove the token from storage
        return Success("Logout successful");
    }

    /// <summary>
    /// Test Active Directory connectivity
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>AD connectivity test result</returns>
    [HttpGet("test-ad")]
    [AllowAnonymous]
    [ProducesResponseType(typeof(object), 200)]
    public async Task<ActionResult<object>> TestActiveDirectory(CancellationToken cancellationToken)
    {
        try
        {
            Logger.LogInformation("Testing Active Directory connectivity");

            var isConnected = await _activeDirectoryService.TestConnectionAsync(cancellationToken);

            var result = new
            {
                IsConnected = isConnected,
                Message = isConnected ? "Active Directory connection successful" : "Active Directory connection failed",
                Timestamp = DateTime.UtcNow
            };

            if (isConnected)
            {
                Logger.LogInformation("Active Directory connectivity test successful");
                return Success(result, "AD connectivity test completed");
            }
            else
            {
                Logger.LogWarning("Active Directory connectivity test failed");
                return StatusCode(503, new ApiErrorResponse
                {
                    StatusCode = 503,
                    Title = "Service Unavailable",
                    Detail = "Active Directory service is not available"
                });
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error during Active Directory connectivity test");
            return StatusCode(500, new ApiErrorResponse
            {
                StatusCode = 500,
                Title = "Test Error",
                Detail = "An error occurred during AD connectivity test"
            });
        }
    }

    /// <summary>
    /// Get current user information
    /// </summary>
    /// <returns>Current user information</returns>
    [HttpGet("me")]
    [Authorize]
    [ProducesResponseType(typeof(CurrentUserInfo), 200)]
    public ActionResult<CurrentUserInfo> GetCurrentUser()
    {
        var userInfo = new CurrentUserInfo
        {
            UserId = GetCurrentUserId() ?? "",
            Username = GetCurrentUsername() ?? "",
            Email = User?.FindFirst(ClaimTypes.Email)?.Value ?? "",
            FullName = $"{User?.FindFirst(ClaimTypes.GivenName)?.Value} {User?.FindFirst(ClaimTypes.Surname)?.Value}".Trim(),
            Role = GetCurrentUserRole() ?? "",
            FactoryId = GetCurrentUserFactoryId()?.ToString()
        };

        return Success(userInfo);
    }

    private string GenerateJwtToken(string userId, string username, string email, string role, string factoryId)
    {
        var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_configuration["Jwt:Key"] ?? throw new InvalidOperationException("JWT Key not configured")));
        var credentials = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

        var claims = new[]
        {
            new Claim(ClaimTypes.NameIdentifier, userId),
            new Claim(ClaimTypes.Name, username),
            new Claim(ClaimTypes.Email, email),
            new Claim(ClaimTypes.Role, role),
            new Claim("FactoryId", factoryId),
            new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
            new Claim(JwtRegisteredClaimNames.Iat, DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString(), ClaimValueTypes.Integer64)
        };

        var token = new JwtSecurityToken(
            issuer: _configuration["Jwt:Issuer"],
            audience: _configuration["Jwt:Audience"],
            claims: claims,
            expires: DateTime.UtcNow.AddHours(8),
            signingCredentials: credentials
        );

        return new JwtSecurityTokenHandler().WriteToken(token);
    }


}

/// <summary>
/// Login request model
/// </summary>
public class LoginRequest
{
    /// <summary>
    /// Username
    /// </summary>
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// Password
    /// </summary>
    public string Password { get; set; } = string.Empty;
}

/// <summary>
/// Authentication result model
/// </summary>
public class AuthenticationResult
{
    /// <summary>
    /// JWT token
    /// </summary>
    public string Token { get; set; } = string.Empty;

    /// <summary>
    /// Username
    /// </summary>
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// Email address
    /// </summary>
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// Full name
    /// </summary>
    public string FullName { get; set; } = string.Empty;

    /// <summary>
    /// User role
    /// </summary>
    public string Role { get; set; } = string.Empty;

    /// <summary>
    /// Factory ID
    /// </summary>
    public string? FactoryId { get; set; }

    /// <summary>
    /// Token expiration time
    /// </summary>
    public DateTime ExpiresAt { get; set; }
}

/// <summary>
/// Current user information model
/// </summary>
public class CurrentUserInfo
{
    /// <summary>
    /// User ID
    /// </summary>
    public string UserId { get; set; } = string.Empty;

    /// <summary>
    /// Username
    /// </summary>
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// Email address
    /// </summary>
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// Full name
    /// </summary>
    public string FullName { get; set; } = string.Empty;

    /// <summary>
    /// User role
    /// </summary>
    public string Role { get; set; } = string.Empty;

    /// <summary>
    /// Factory ID
    /// </summary>
    public string? FactoryId { get; set; }
}
