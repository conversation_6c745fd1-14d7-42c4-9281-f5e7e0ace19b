using FluentValidation;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using System.Reflection;
using HWSAuditPlatform.Application.Behaviors;
using HWSAuditPlatform.Application.Users.Services;
using HWSAuditPlatform.Application.Scheduling.Services;

namespace HWSAuditPlatform.Application;

/// <summary>
/// Dependency injection configuration for the Application layer
/// </summary>
public static class DependencyInjection
{
    /// <summary>
    /// Adds Application layer services to the dependency injection container
    /// </summary>
    /// <param name="services">The service collection</param>
    /// <returns>The service collection for chaining</returns>
    public static IServiceCollection AddApplication(this IServiceCollection services)
    {
        var assembly = Assembly.GetExecutingAssembly();

        // Add Application Services
        services.AddScoped<Users.Services.IUserService, Users.Services.UserService>();

        // Add FluentValidation
        services.AddValidatorsFromAssembly(assembly);

        // Cross-cutting concerns will be handled within services or through decorators

        // Add application services
        services.AddScoped<IAdRoleMappingService, AdRoleMappingService>();
        services.AddScoped<Domain.Services.ITemplateAccessService, Users.Services.TemplateAccessService>();
        services.AddScoped<IRecurrenceCalculationService, RecurrenceCalculationService>();

        return services;
    }
}
