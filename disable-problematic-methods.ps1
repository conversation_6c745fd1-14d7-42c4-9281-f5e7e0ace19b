# PowerShell script to temporarily disable problematic methods by commenting them out

$controllersPath = "src\HWSAuditPlatform.ApiService\Controllers"

# Get all controller files
$controllerFiles = Get-ChildItem -Path $controllersPath -Filter "*.cs"

foreach ($file in $controllerFiles) {
    $filePath = $file.FullName
    $content = Get-Content $filePath -Raw
    $modified = $false
    
    # Skip already processed files (BaseController, UsersController, AuthController)
    if ($file.Name -in @("BaseController.cs", "UsersController.cs")) {
        continue
    }
    
    Write-Host "Processing $($file.Name)..."
    
    # Comment out methods that use MediatR or missing commands/queries
    $patterns = @(
        'await Mediator\.Send\(',
        'Mediator\.Send\(',
        'new.*Command\s*\(',
        'new.*Query\s*\(',
        'Command\s+\w+',
        'Query\s+\w+'
    )
    
    foreach ($pattern in $patterns) {
        if ($content -match $pattern) {
            Write-Host "  Found problematic pattern: $pattern"
            $modified = $true
        }
    }
    
    if ($modified) {
        # Add a comment at the top indicating the file needs service implementation
        $lines = $content -split "`r?`n"
        $newContent = @()
        $inMethod = $false
        $braceCount = 0
        
        # Add header comment
        $newContent += "// TODO: This controller needs to be migrated to use service pattern instead of MediatR"
        $newContent += "// Many methods are temporarily disabled until proper services are implemented"
        $newContent += ""
        
        for ($i = 0; $i -lt $lines.Count; $i++) {
            $line = $lines[$i]
            
            # Check if this line starts a method that uses problematic patterns
            if ($line -match 'public.*Task.*\(' -and !$inMethod) {
                # Look ahead to see if this method contains problematic patterns
                $methodHasIssues = $false
                $lookAhead = 20 # Look ahead 20 lines to check for issues
                for ($j = $i; $j -lt [Math]::Min($i + $lookAhead, $lines.Count); $j++) {
                    foreach ($pattern in $patterns) {
                        if ($lines[$j] -match $pattern) {
                            $methodHasIssues = $true
                            break
                        }
                    }
                    if ($methodHasIssues) { break }
                }
                
                if ($methodHasIssues) {
                    $newContent += "        // TODO: Method temporarily disabled - needs service implementation"
                    $newContent += "        /*"
                    $inMethod = $true
                    $braceCount = 0
                }
            }
            
            if ($inMethod) {
                # Count braces to find method end
                $openBraces = ($line -split '\{').Count - 1
                $closeBraces = ($line -split '\}').Count - 1
                $braceCount += $openBraces - $closeBraces
                
                $newContent += $line
                
                # If we've closed all braces, end the comment
                if ($braceCount -le 0 -and $line -match '\}') {
                    $newContent += "        */"
                    $inMethod = $false
                }
            } else {
                $newContent += $line
            }
        }
        
        # Write the modified content back
        $newContent -join "`r`n" | Set-Content $filePath -NoNewline
        Write-Host "  Modified $($file.Name)"
    }
}

Write-Host "Method disabling completed!"
