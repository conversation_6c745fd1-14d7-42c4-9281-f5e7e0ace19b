using HWSAuditPlatform.Application.Audits.DTOs;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.Application.Audits.Services;

/// <summary>
/// Service interface for audit operations
/// </summary>
public interface IAuditService
{
    // Audit CRUD Operations
    Task<string> CreateAuditAsync(CreateAuditRequest request, CancellationToken cancellationToken = default);
    Task<AuditDto> GetAuditAsync(GetAuditRequest request, CancellationToken cancellationToken = default);
    Task<PaginatedResult<AuditSummaryDto>> GetAuditsAsync(GetAuditsRequest request, CancellationToken cancellationToken = default);
    Task<PaginatedResult<AuditSummaryDto>> GetMyAuditsAsync(GetMyAuditsRequest request, CancellationToken cancellationToken = default);
    Task<PaginatedResult<AuditSummaryDto>> GetMyAuditHistoryAsync(GetMyAuditHistoryRequest request, CancellationToken cancellationToken = default);
    Task<PaginatedResult<AuditReviewDto>> GetCompletedAuditsForReviewAsync(GetCompletedAuditsForReviewRequest request, CancellationToken cancellationToken = default);
    Task<AuditReviewDto> GetAuditForReviewAsync(GetAuditForReviewRequest request, CancellationToken cancellationToken = default);

    // Audit Lifecycle Operations
    Task StartAuditAsync(StartAuditRequest request, CancellationToken cancellationToken = default);
    Task SubmitAuditAsync(SubmitAuditRequest request, CancellationToken cancellationToken = default);
    Task ReviewAuditAsync(ReviewAuditRequest request, CancellationToken cancellationToken = default);

    // Audit Answer Operations
    Task<string> SubmitAuditAnswerAsync(SubmitAuditAnswerRequest request, CancellationToken cancellationToken = default);
    Task DeleteAuditAnswerAsync(DeleteAuditAnswerRequest request, CancellationToken cancellationToken = default);
    Task<List<AuditAnswerDto>> GetAuditAnswersAsync(GetAuditAnswersRequest request, CancellationToken cancellationToken = default);
    Task<AuditAnswerDto> GetAuditAnswerAsync(GetAuditAnswerRequest request, CancellationToken cancellationToken = default);
}

/// <summary>
/// Request model for creating audit
/// </summary>
public class CreateAuditRequest : BaseRequest
{
    public int AuditTemplateId { get; set; }
    public AssignmentType AssignmentType { get; set; }
    public string? AssignedToUserGroupId { get; set; }
    public string? AssignedToUserId { get; set; }
    public DateTime ScheduledDate { get; set; }
    public DateTime? DueDate { get; set; }
    public int FactoryId { get; set; }
    public int AreaId { get; set; }
    public int? SubAreaId { get; set; }
}

/// <summary>
/// Request model for getting audit
/// </summary>
public class GetAuditRequest : BaseRequest
{
    public string AuditId { get; set; } = string.Empty;
}

/// <summary>
/// Request model for getting audits
/// </summary>
public class GetAuditsRequest : BaseRequest
{
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public string? SearchTerm { get; set; }
    public AuditOverallStatus? Status { get; set; }
    public string? AssignedToUserId { get; set; }
    public int? FactoryId { get; set; }
    public int? AreaId { get; set; }
    public int? AuditTemplateId { get; set; }
    public DateTime? ScheduledDateFrom { get; set; }
    public DateTime? ScheduledDateTo { get; set; }
    public DateTime? DueDateFrom { get; set; }
    public DateTime? DueDateTo { get; set; }
    public bool? IsOverdue { get; set; }
    public string? SortBy { get; set; }
    public string? SortDirection { get; set; }
}

/// <summary>
/// Request model for getting my audits
/// </summary>
public class GetMyAuditsRequest : BaseRequest
{
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public AuditOverallStatus? Status { get; set; }
    public int? FactoryId { get; set; }
    public int? AreaId { get; set; }
    public DateTime? ScheduledDateFrom { get; set; }
    public DateTime? ScheduledDateTo { get; set; }
    public DateTime? DueDateFrom { get; set; }
    public DateTime? DueDateTo { get; set; }
    public bool? IsOverdue { get; set; }
    public string? SortBy { get; set; }
    public string? SortDirection { get; set; }
}

/// <summary>
/// Request model for getting my audit history
/// </summary>
public class GetMyAuditHistoryRequest : BaseRequest
{
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public int? FactoryId { get; set; }
    public int? AreaId { get; set; }
    public DateTime? CompletedDateFrom { get; set; }
    public DateTime? CompletedDateTo { get; set; }
    public string? SortBy { get; set; }
    public string? SortDirection { get; set; }
}

/// <summary>
/// Request model for getting completed audits for review
/// </summary>
public class GetCompletedAuditsForReviewRequest : BaseRequest
{
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public int? FactoryId { get; set; }
    public int? AreaId { get; set; }
    public DateTime? CompletedDateFrom { get; set; }
    public DateTime? CompletedDateTo { get; set; }
    public string? SortBy { get; set; }
    public string? SortDirection { get; set; }
}

/// <summary>
/// Request model for getting audit for review
/// </summary>
public class GetAuditForReviewRequest : BaseRequest
{
    public string AuditId { get; set; } = string.Empty;
}

/// <summary>
/// Request model for starting audit
/// </summary>
public class StartAuditRequest : BaseRequest
{
    public string AuditId { get; set; } = string.Empty;
}

/// <summary>
/// Request model for submitting audit
/// </summary>
public class SubmitAuditRequest : BaseRequest
{
    public string AuditId { get; set; } = string.Empty;
    public string? AuditorComments { get; set; }
}

/// <summary>
/// Request model for reviewing audit
/// </summary>
public class ReviewAuditRequest : BaseRequest
{
    public string AuditId { get; set; } = string.Empty;
    public bool Approved { get; set; }
    public string? Comments { get; set; }
}

/// <summary>
/// Request model for submitting audit answer
/// </summary>
public class SubmitAuditAnswerRequest : BaseRequest
{
    public string AuditId { get; set; } = string.Empty;
    public int QuestionId { get; set; }
    public string? AnswerValue { get; set; }
    public bool IsNotApplicable { get; set; }
    public string? Comments { get; set; }
    public SeverityLevel? SeverityLevel { get; set; }
    public List<int> SelectedOptionIds { get; set; } = new();
    public List<string> FailureReasons { get; set; } = new();
}

/// <summary>
/// Request model for deleting audit answer
/// </summary>
public class DeleteAuditAnswerRequest : BaseRequest
{
    public string AuditAnswerId { get; set; } = string.Empty;
}

/// <summary>
/// Request model for getting audit answers
/// </summary>
public class GetAuditAnswersRequest : BaseRequest
{
    public string AuditId { get; set; } = string.Empty;
    public int? QuestionId { get; set; }
}

/// <summary>
/// Request model for getting audit answer
/// </summary>
public class GetAuditAnswerRequest : BaseRequest
{
    public string AuditAnswerId { get; set; } = string.Empty;
}
