using MediatR;
using Microsoft.AspNetCore.Mvc;
using HWSAuditPlatform.ApiService.Models;
// using HWSAuditPlatform.Application.Organization.Commands.CreateAreaResponsibility;
using HWSAuditPlatform.Application.Organization.DTOs;
// using HWSAuditPlatform.Application.Organization.Queries.GetAreaResponsibilities;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.ApiService.Controllers;

/// <summary>
/// Controller for managing area-based responsibility assignments
/// </summary>
[Route("api/v1/organization/area-responsibilities")]
[Tags("Area Responsibility Management")]
public class AreaResponsibilityController : BaseController
{
    public AreaResponsibilityController(ILogger<AreaResponsibilityController> logger)
        : base(logger)
    {
    }

    /// <summary>
    /// Gets area responsibilities with optional filtering
    /// </summary>
    /// <param name="areaId">Filter by specific area ID</param>
    /// <param name="auditTemplateId">Filter by specific audit template ID</param>
    /// <param name="responsibilityType">Filter by responsibility type</param>
    /// <param name="responsibleUserId">Filter by responsible user ID</param>
    /// <param name="isActive">Filter by active status</param>
    /// <param name="includeTemplateSpecific">Include template-specific responsibilities</param>
    /// <param name="includeGeneral">Include general responsibilities</param>
    /// <returns>List of area responsibilities</returns>
    [HttpGet]
    [ProducesResponseType(typeof(ApiResponse<List<AreaResponsibilityDto>>), 200)]
    public async Task<ActionResult<List<AreaResponsibilityDto>>> GetAreaResponsibilities(
        [FromQuery] int? areaId = null,
        [FromQuery] int? auditTemplateId = null,
        [FromQuery] ResponsibilityType? responsibilityType = null,
        [FromQuery] string? responsibleUserId = null,
        [FromQuery] bool? isActive = true,
        [FromQuery] bool includeTemplateSpecific = true,
        [FromQuery] bool includeGeneral = true)
    {
        var query = new GetAreaResponsibilitiesQuery
        {
            AreaId = areaId,
            AuditTemplateId = auditTemplateId,
            ResponsibilityType = responsibilityType,
            ResponsibleUserId = responsibleUserId,
            IsActive = isActive,
            IncludeTemplateSpecific = includeTemplateSpecific,
            IncludeGeneral = includeGeneral
        };

        var result = await Mediator.Send(query);
        return Success(result, "Area responsibilities retrieved successfully");
    }

    /// <summary>
    /// Creates a new area responsibility assignment
    /// </summary>
    /// <param name="command">Area responsibility creation data</param>
    /// <returns>Created area responsibility</returns>
    [HttpPost]
    [ProducesResponseType(typeof(ApiResponse<AreaResponsibilityDto>), 201)]
    [ProducesResponseType(typeof(ApiErrorResponse), 400)]
    [ProducesResponseType(typeof(ApiErrorResponse), 404)]
    [ProducesResponseType(typeof(ApiErrorResponse), 409)]
    public async Task<ActionResult<AreaResponsibilityDto>> CreateAreaResponsibility(
        [FromBody] CreateAreaResponsibilityCommand command)
    {
        var result = await Mediator.Send(command);
        return Created(nameof(GetAreaResponsibility), new { id = result.Id }, result, 
            "Area responsibility created successfully");
    }

    /// <summary>
    /// Gets a specific area responsibility by ID
    /// </summary>
    /// <param name="id">Area responsibility ID</param>
    /// <returns>Area responsibility details</returns>
    [HttpGet("{id}")]
    [ProducesResponseType(typeof(ApiResponse<AreaResponsibilityDto>), 200)]
    [ProducesResponseType(typeof(ApiErrorResponse), 404)]
    public async Task<ActionResult<AreaResponsibilityDto>> GetAreaResponsibility(string id)
    {
        var query = new GetAreaResponsibilitiesQuery();
        var responsibilities = await Mediator.Send(query);
        var responsibility = responsibilities.FirstOrDefault(r => r.Id == id);

        if (responsibility == null)
        {
            return NotFound(ApiErrorResponse.NotFound($"Area responsibility with ID '{id}' was not found."));
        }

        return Success(responsibility, "Area responsibility retrieved successfully");
    }

    /// <summary>
    /// Updates an existing area responsibility
    /// </summary>
    /// <param name="id">Area responsibility ID</param>
    /// <param name="command">Updated area responsibility data</param>
    /// <returns>Updated area responsibility</returns>
    [HttpPut("{id}")]
    [ProducesResponseType(typeof(ApiResponse<AreaResponsibilityDto>), 200)]
    [ProducesResponseType(typeof(ApiErrorResponse), 400)]
    [ProducesResponseType(typeof(ApiErrorResponse), 404)]
    public async Task<ActionResult<AreaResponsibilityDto>> UpdateAreaResponsibility(
        string id, [FromBody] CreateAreaResponsibilityCommand command)
    {
        // For now, we'll implement this as delete and recreate
        // In a full implementation, you'd want a dedicated UpdateAreaResponsibilityCommand
        Logger.LogInformation("Update area responsibility {Id} requested", id);
        
        // This would need a proper UpdateAreaResponsibilityCommand in a full implementation
        return BadRequest(ApiErrorResponse.BadRequest("Update functionality not yet implemented. Please delete and recreate the responsibility."));
    }

    /// <summary>
    /// Deletes an area responsibility
    /// </summary>
    /// <param name="id">Area responsibility ID</param>
    /// <returns>Success confirmation</returns>
    [HttpDelete("{id}")]
    [ProducesResponseType(typeof(ApiResponse<object>), 200)]
    [ProducesResponseType(typeof(ApiErrorResponse), 404)]
    public async Task<ActionResult> DeleteAreaResponsibility(string id)
    {
        // This would need a DeleteAreaResponsibilityCommand in a full implementation
        Logger.LogInformation("Delete area responsibility {Id} requested", id);
        
        return BadRequest(ApiErrorResponse.BadRequest("Delete functionality not yet implemented."));
    }

    /// <summary>
    /// Gets available responsibility types
    /// </summary>
    /// <returns>List of responsibility types</returns>
    [HttpGet("responsibility-types")]
    [ProducesResponseType(typeof(ApiResponse<List<object>>), 200)]
    public ActionResult<List<object>> GetResponsibilityTypes()
    {
        var types = Enum.GetValues<ResponsibilityType>()
            .Select(rt => (object)new
            {
                Value = (int)rt,
                Name = rt.ToString(),
                Description = GetResponsibilityTypeDescription(rt)
            })
            .ToList();

        return Success(types, "Responsibility types retrieved successfully");
    }

    private static string GetResponsibilityTypeDescription(ResponsibilityType type)
    {
        return type switch
        {
            ResponsibilityType.CorrectiveActionOwner => "Responsible for managing and implementing corrective actions",
            ResponsibilityType.RetrospectiveAnalyst => "Responsible for performing retrospective efficiency analysis",
            ResponsibilityType.FindingReviewer => "Responsible for reviewing and validating findings",
            ResponsibilityType.EscalationContact => "Contact person for escalations and critical issues",
            _ => type.ToString()
        };
    }
}
