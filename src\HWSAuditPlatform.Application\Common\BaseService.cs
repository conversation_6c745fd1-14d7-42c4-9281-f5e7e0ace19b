using Microsoft.Extensions.Logging;
using HWSAuditPlatform.Application.Interfaces;

namespace HWSAuditPlatform.Application.Common;

/// <summary>
/// Base class for application services with common functionality
/// </summary>
/// <typeparam name="TService">The service type for logging</typeparam>
public abstract class BaseService<TService> : IBaseService
{
    protected readonly ILogger<TService> Logger;
    protected readonly IApplicationDbContext Context;
    protected readonly ICurrentUserService CurrentUserService;

    protected BaseService(
        IApplicationDbContext context,
        ICurrentUserService currentUserService,
        ILogger<TService> logger)
    {
        Context = context;
        CurrentUserService = currentUserService;
        Logger = logger;
    }

    /// <summary>
    /// Validates a request and throws ValidationException if invalid
    /// </summary>
    /// <param name="request">The request to validate</param>
    /// <param name="cancellationToken">Cancellation token</param>
    protected virtual Task ValidateRequestAsync<TRequest>(TRequest request, CancellationToken cancellationToken = default)
    {
        // Base implementation - can be overridden by derived services
        return Task.CompletedTask;
    }

    /// <summary>
    /// Logs the start of an operation
    /// </summary>
    /// <param name="operationName">Name of the operation</param>
    /// <param name="parameters">Optional parameters to log</param>
    protected void LogOperationStart(string operationName, object? parameters = null)
    {
        if (parameters != null)
        {
            Logger.LogInformation("Starting {OperationName} with parameters: {@Parameters}", operationName, parameters);
        }
        else
        {
            Logger.LogInformation("Starting {OperationName}", operationName);
        }
    }

    /// <summary>
    /// Logs the completion of an operation
    /// </summary>
    /// <param name="operationName">Name of the operation</param>
    /// <param name="result">Optional result to log</param>
    protected void LogOperationComplete(string operationName, object? result = null)
    {
        if (result != null)
        {
            Logger.LogInformation("Completed {OperationName} with result: {@Result}", operationName, result);
        }
        else
        {
            Logger.LogInformation("Completed {OperationName}", operationName);
        }
    }

    /// <summary>
    /// Logs an operation error
    /// </summary>
    /// <param name="operationName">Name of the operation</param>
    /// <param name="exception">The exception that occurred</param>
    protected void LogOperationError(string operationName, Exception exception)
    {
        Logger.LogError(exception, "Error in {OperationName}: {ErrorMessage}", operationName, exception.Message);
    }
}
