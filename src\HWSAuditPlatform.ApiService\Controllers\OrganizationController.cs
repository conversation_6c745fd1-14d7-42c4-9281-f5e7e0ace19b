using Asp.Versioning;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Organization.DTOs;
// using HWSAuditPlatform.Application.Organization.Commands.CreateFactory;
// using HWSAuditPlatform.Application.Organization.Commands.CreateArea;
// using HWSAuditPlatform.Application.Organization.Commands.CreateSubArea;
// using HWSAuditPlatform.Application.Organization.Commands.UpdateFactory;
// using HWSAuditPlatform.Application.Organization.Queries.GetLocations;
// using HWSAuditPlatform.Application.Organization.Queries.GetFactories;
// using HWSAuditPlatform.Application.Organization.Queries.GetFactory;
// using HWSAuditPlatform.Application.Organization.Queries.GetAreasByFactory;
// using HWSAuditPlatform.Application.Organization.Queries.GetSubAreasByArea;
// using HWSAuditPlatform.Application.Organization.Queries.GetOrganizationHierarchy;

namespace HWSAuditPlatform.ApiService.Controllers;

/// <summary>
/// Controller for organizational structure management
/// </summary>
[Route("api/v{version:apiVersion}/[controller]")]
[ApiVersion("1.0")]
public class OrganizationController : BaseController
{
    public OrganizationController(ILogger<OrganizationController> logger) 
        : base(logger)
    {
    }

    /// <summary>
    /// Get all locations
    /// </summary>
    /// <param name="ownerGroupId">Optional owner group filter</param>
    /// <param name="includeInactive">Include inactive locations</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of locations</returns>
    [HttpGet("locations")]
    [Authorize(Policy = "AuditorOrAbove")]
    [ProducesResponseType(typeof(List<LocationSummaryDto>), 200)]
    public async Task<ActionResult<List<LocationSummaryDto>>> GetLocations(
        [FromQuery] string? ownerGroupId = null,
        [FromQuery] bool includeInactive = false,
        CancellationToken cancellationToken = default)
    {
        Logger.LogInformation("Getting all locations - OwnerGroupId: {OwnerGroupId}, IncludeInactive: {IncludeInactive}",
            ownerGroupId, includeInactive);

        var query = new GetLocationsQuery
        {
            OwnerGroupId = ownerGroupId,
            IncludeInactive = includeInactive
        };
        var result = await Mediator.Send(query, cancellationToken);
        return Success(result);
    }

    /// <summary>
    /// Get factories for a specific location
    /// </summary>
    /// <param name="locationId">Location ID</param>
    /// <param name="isActive">Optional active status filter</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of factories</returns>
    [HttpGet("locations/{locationId}/factories")]
    [Authorize(Policy = "AuditorOrAbove")]
    [ProducesResponseType(typeof(List<FactorySummaryDto>), 200)]
    public async Task<ActionResult<List<FactorySummaryDto>>> GetFactoriesByLocation(
        int locationId,
        [FromQuery] bool? isActive = null,
        CancellationToken cancellationToken = default)
    {
        Logger.LogInformation("Getting factories for location: {LocationId}, IsActive: {IsActive}", locationId, isActive);

        var query = new GetFactoriesQuery
        {
            LocationId = locationId,
            IsActive = isActive
        };
        var result = await Mediator.Send(query, cancellationToken);
        return Success(result);
    }

    /// <summary>
    /// Get all factories (with optional filtering)
    /// </summary>
    /// <param name="locationId">Optional location ID filter</param>
    /// <param name="isActive">Optional active status filter</param>
    /// <param name="searchTerm">Optional search term</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of factories</returns>
    [HttpGet("factories")]
    [Authorize(Policy = "AuditorOrAbove")]
    [ProducesResponseType(typeof(List<FactorySummaryDto>), 200)]
    public async Task<ActionResult<List<FactorySummaryDto>>> GetFactories(
        [FromQuery] int? locationId = null,
        [FromQuery] bool? isActive = null,
        [FromQuery] string? searchTerm = null,
        CancellationToken cancellationToken = default)
    {
        Logger.LogInformation("Getting factories - LocationId: {LocationId}, IsActive: {IsActive}, SearchTerm: {SearchTerm}",
            locationId, isActive, searchTerm);

        // For non-admin users, filter by their assigned factory
        var currentUserFactoryId = GetCurrentUserFactoryId();
        int? restrictToFactoryId = null;
        if (!HasAnyRole("DevAdmin") && currentUserFactoryId.HasValue)
        {
            restrictToFactoryId = currentUserFactoryId.Value;
        }

        var query = new GetFactoriesQuery
        {
            LocationId = locationId,
            IsActive = isActive,
            SearchTerm = searchTerm,
            RestrictToFactoryId = restrictToFactoryId
        };
        var result = await Mediator.Send(query, cancellationToken);
        return Success(result);
    }

    /// <summary>
    /// Get areas for a specific factory
    /// </summary>
    /// <param name="factoryId">Factory ID</param>
    /// <param name="isActive">Optional active status filter</param>
    /// <param name="searchTerm">Optional search term</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of areas</returns>
    [HttpGet("factories/{factoryId}/areas")]
    [Authorize(Policy = "AuditorOrAbove")]
    [ProducesResponseType(typeof(List<AreaSummaryDto>), 200)]
    public async Task<ActionResult<List<AreaSummaryDto>>> GetAreasByFactory(
        int factoryId,
        [FromQuery] bool? isActive = null,
        [FromQuery] string? searchTerm = null,
        CancellationToken cancellationToken = default)
    {
        Logger.LogInformation("Getting areas for factory: {FactoryId}, IsActive: {IsActive}, SearchTerm: {SearchTerm}",
            factoryId, isActive, searchTerm);

        // Check if user has access to this factory
        var currentUserFactoryId = GetCurrentUserFactoryId();
        if (!HasAnyRole("DevAdmin") && currentUserFactoryId.HasValue && currentUserFactoryId.Value != factoryId)
        {
            Logger.LogWarning("User attempted to access factory {FactoryId} but is assigned to factory {UserFactoryId}",
                factoryId, currentUserFactoryId);
            return Forbid();
        }

        var query = new GetAreasByFactoryQuery
        {
            FactoryId = factoryId,
            IsActive = isActive,
            SearchTerm = searchTerm
        };
        var result = await Mediator.Send(query, cancellationToken);
        return Success(result);
    }

    /// <summary>
    /// Get sub-areas for a specific area
    /// </summary>
    /// <param name="areaId">Area ID</param>
    /// <param name="isActive">Optional active status filter</param>
    /// <param name="searchTerm">Optional search term</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of sub-areas</returns>
    [HttpGet("areas/{areaId}/subareas")]
    [Authorize(Policy = "AuditorOrAbove")]
    [ProducesResponseType(typeof(List<SubAreaSummaryDto>), 200)]
    public async Task<ActionResult<List<SubAreaSummaryDto>>> GetSubAreasByArea(
        int areaId,
        [FromQuery] bool? isActive = null,
        [FromQuery] string? searchTerm = null,
        CancellationToken cancellationToken = default)
    {
        Logger.LogInformation("Getting sub-areas for area: {AreaId}, IsActive: {IsActive}, SearchTerm: {SearchTerm}",
            areaId, isActive, searchTerm);

        var query = new GetSubAreasByAreaQuery
        {
            AreaId = areaId,
            IsActive = isActive,
            SearchTerm = searchTerm
        };
        var result = await Mediator.Send(query, cancellationToken);
        return Success(result);
    }

    /// <summary>
    /// Get the complete organizational hierarchy
    /// </summary>
    /// <param name="locationId">Optional location ID filter</param>
    /// <param name="includeInactive">Include inactive entities</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Hierarchical organization structure</returns>
    [HttpGet("hierarchy")]
    [Authorize(Policy = "AuditorOrAbove")]
    [ProducesResponseType(typeof(List<LocationDto>), 200)]
    public async Task<ActionResult<List<LocationDto>>> GetOrganizationHierarchy(
        [FromQuery] int? locationId = null,
        [FromQuery] bool includeInactive = false,
        CancellationToken cancellationToken = default)
    {
        Logger.LogInformation("Getting organization hierarchy - LocationId: {LocationId}, IncludeInactive: {IncludeInactive}",
            locationId, includeInactive);

        var query = new GetOrganizationHierarchyQuery
        {
            LocationId = locationId,
            IncludeInactive = includeInactive
        };
        var result = await Mediator.Send(query, cancellationToken);
        return Success(result);
    }

    /// <summary>
    /// Create a new factory
    /// </summary>
    /// <param name="command">Factory creation data</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Created factory ID</returns>
    [HttpPost("factories")]
    [Authorize(Policy = "DevAdminOnly")]
    [ProducesResponseType(typeof(int), 201)]
    public async Task<ActionResult<int>> CreateFactory(
        CreateFactoryCommand command,
        CancellationToken cancellationToken)
    {
        Logger.LogInformation("Creating new factory: {FactoryName}", command.FactoryName);

        var factoryId = await Mediator.Send(command, cancellationToken);
        return Created(nameof(GetFactory), new { factoryId }, factoryId, "Factory created successfully");
    }

    /// <summary>
    /// Create a new area
    /// </summary>
    /// <param name="factoryId">Factory ID</param>
    /// <param name="command">Area creation data</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Created area ID</returns>
    [HttpPost("factories/{factoryId}/areas")]
    [Authorize(Policy = "DevAdminOnly")]
    [ProducesResponseType(typeof(int), 201)]
    public async Task<ActionResult<int>> CreateArea(
        int factoryId,
        CreateAreaCommand command,
        CancellationToken cancellationToken)
    {
        Logger.LogInformation("Creating new area for factory: {FactoryId}, AreaName: {AreaName}",
            factoryId, command.AreaName);

        command.FactoryId = factoryId;
        var areaId = await Mediator.Send(command, cancellationToken);
        return Created(nameof(GetAreasByFactory), new { factoryId }, areaId, "Area created successfully");
    }

    /// <summary>
    /// Create a new sub-area
    /// </summary>
    /// <param name="areaId">Area ID</param>
    /// <param name="command">Sub-area creation data</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Created sub-area ID</returns>
    [HttpPost("areas/{areaId}/subareas")]
    [Authorize(Policy = "SystemManagerOrAbove")]
    [ProducesResponseType(typeof(int), 201)]
    public async Task<ActionResult<int>> CreateSubArea(
        int areaId,
        CreateSubAreaCommand command,
        CancellationToken cancellationToken)
    {
        Logger.LogInformation("Creating new sub-area for area: {AreaId}, SubAreaName: {SubAreaName}",
            areaId, command.SubAreaName);

        command.AreaId = areaId;
        var subAreaId = await Mediator.Send(command, cancellationToken);
        return Created(nameof(GetSubAreasByArea), new { areaId }, subAreaId, "Sub-area created successfully");
    }

    /// <summary>
    /// Get a specific factory by ID
    /// </summary>
    /// <param name="factoryId">Factory ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Factory details</returns>
    [HttpGet("factories/{factoryId}")]
    [Authorize(Policy = "AuditorOrAbove")]
    [ProducesResponseType(typeof(FactoryDto), 200)]
    public async Task<ActionResult<FactoryDto>> GetFactory(
        int factoryId,
        CancellationToken cancellationToken)
    {
        Logger.LogInformation("Getting factory: {FactoryId}", factoryId);

        // Check if user has access to this factory
        var currentUserFactoryId = GetCurrentUserFactoryId();
        if (!HasAnyRole("DevAdmin") && currentUserFactoryId.HasValue && currentUserFactoryId.Value != factoryId)
        {
            Logger.LogWarning("User attempted to access factory {FactoryId} but is assigned to factory {UserFactoryId}",
                factoryId, currentUserFactoryId);
            return Forbid();
        }

        var query = new GetFactoryQuery { FactoryId = factoryId };
        var result = await Mediator.Send(query, cancellationToken);
        return Success(result);
    }

    /// <summary>
    /// Update a factory
    /// </summary>
    /// <param name="factoryId">Factory ID</param>
    /// <param name="command">Factory update data</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>No content</returns>
    [HttpPut("factories/{factoryId}")]
    [Authorize(Policy = "DevAdminOnly")]
    [ProducesResponseType(204)]
    public async Task<ActionResult> UpdateFactory(
        int factoryId,
        UpdateFactoryCommand command,
        CancellationToken cancellationToken)
    {
        Logger.LogInformation("Updating factory: {FactoryId}", factoryId);

        command.Id = factoryId;
        await Mediator.Send(command, cancellationToken);
        return NoContent();
    }
}
