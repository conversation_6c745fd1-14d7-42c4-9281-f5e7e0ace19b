# HWS Audit Platform - TODO List

This document contains all TODO items found throughout the codebase that need to be implemented or completed.

## 🚨 **HIGH PRIORITY - API Service Layer**

### **Authentication & User Management**
- **AuthController.cs:141** - Implement user creation service method
- **AuthController.cs:321** - Implement user service method for getting user by ID

### **Audit Management**
- **AuditsController.cs:355** - Implement service method for getting audit answers
- **AuditsController.cs:379** - Implement service method for getting specific audit answer
- **AuditsController.cs:402** - Implement service method for deleting audit answer
- **AuditsController.cs:461** - Implement service method for getting completed audits for review
- **AuditsController.cs:482** - Implement service method for getting audit for review
- **AuditsController.cs:527** - Implement service method for getting audit history

### **Application Layer**
- **AuditService.cs:892** - Create finding if needed (requires IFindingService implementation)

## 🔧 **INFRASTRUCTURE & BACKGROUND SERVICES**

### **Domain Event Handling**
- **DomainEventService.cs:9** - Implement proper domain event handling mechanism to replace MediatR
- **DomainEventService.cs:26** - Implement domain event handling logic

### **Scheduler Worker**
- **Worker.cs:134** - Implement AD sync functionality
- **Worker.cs:161** - Implement recurring audit generation functionality

### **Domain Model**
- **Factory.cs:23** - Normalize to Processes and FactoryProcesses tables for querying and integrity

## 🎨 **WEB APPLICATION (WebApp)**

### **Finding Management**
- **FindingCard.razor:369** - Implement view corrective actions functionality
- **FindingDetail.razor:282** - Implement status update modal
- **FindingDetail.razor:288** - Implement category assignment modal
- **FindingDetail.razor:294** - Implement responsibility assignment modal
- **FindingDetail.razor:308** - Implement create corrective action functionality

### **Corrective Actions**
- **CorrectiveActions.razor:170** - Load users dynamically
- **CorrectiveActions.razor:417** - Implement "my-actions" filter when user context is available
- **CorrectiveActions.razor:471** - Implement create action modal
- **CorrectiveActions.razor:477** - Implement status update modal
- **CorrectiveActions.razor:483** - Implement complete action modal
- **CorrectiveActions.razor:489** - Implement verify action modal
- **CorrectiveActionDetail.razor:314** - Implement status update modal
- **CorrectiveActionDetail.razor:320** - Implement complete action modal
- **CorrectiveActionDetail.razor:326** - Implement verify action modal

### **Organization Management**
- **AreaResponsibilityList.razor:168** - Implement edit functionality
- **AreaResponsibilityList.razor:174** - Implement delete functionality with confirmation
- **FindingCategoryList.razor:200** - Implement edit functionality
- **FindingCategoryList.razor:206** - Implement delete functionality with confirmation

### **User Management**
- **ProcessOwnerAssignmentsComponent.razor:185** - Navigate to assignment management page
- **UserListComponent.razor:245** - Navigate to user details page
- **UserListComponent.razor:251** - Navigate to user edit page
- **Users.razor:283** - Implement activate user API call
- **Users.razor:289** - Implement deactivate user API call

### **Template Management**
- **RecurringAudits.razor:87** - Load templates from API
- **TemplateList.razor:303** - Implement activate template API call
- **TemplateList.razor:309** - Implement deactivate template API call
- **TemplateQuestions.razor:640** - Implement question reordering
- **TemplateQuestions.razor:646** - Implement question reordering
- **TemplateQuestions.razor:652** - Implement group editing
- **TemplateQuestions.razor:658** - Implement group deletion
- **TemplateApiService.cs:69** - Update API to include EnableAreaBasedResponsibility in summary DTO

### **Audit Management**
- **AuditDetail.razor:396** - Implement start audit functionality when comprehensive API is available

## 📱 **PWA APPLICATION (WebAuditPWA)**

### **Audit Execution**
- **AttachmentDisplay.razor:410** - If not found offline, try to get from API (for uploaded attachments)
- **QuestionAnswerInput.razor:406** - Store category assignment for the finding
- **ResponsibilityAssignmentModal.razor:274** - Load available users from API
- **ResponsibilityAssignmentModal.razor:310** - Save assignments via API

### **Audit Management**
- **AuditDetails.razor:399** - Get actual option text instead of "Option {id}"
- **AuditDetails.razor:402** - Get actual option texts for multi-select answers
- **AuditHistory.razor:461** - Implement share functionality
- **AuditList.razor:298** - Implement sync functionality
- **AuditList.razor:463** - Show error message to user
- **AuditList.razor:469** - Show error message to user
- **AuditResults.razor:535** - Implement export functionality
- **AuditResults.razor:543** - Implement share functionality

### **Navigation & Sync**
- **NavMenu.razor:315** - Implement actual sync functionality
- **Home.razor:234** - Implement comprehensive sync functionality
- **Home.razor:307** - Show error message to user
- **Home.razor:313** - Show error message to user

### **Service Implementations**
- **ServiceImplementations.cs:604** - Implement actual API call for GetUsersByRole
- **ServiceImplementations.cs:610** - Implement actual API call for GetUsersByArea
- **ServiceImplementations.cs:631** - Implement actual API call for GetFindingCategories
- **ServiceImplementations.cs:637** - Implement actual API call for CreateFindingCategory
- **ServiceImplementations.cs:860** - Implement sync logic
- **ServiceImplementations.cs:866** - Implement sync status
- **ServiceImplementations.cs:872** - Implement pending items count
- **ServiceImplementations.cs:878** - Implement sync status
- **ServiceImplementations.cs:893** - Implement background sync queueing
- **ServiceImplementations.cs:900** - Implement manual sync request
- **ServiceImplementations.cs:907** - Implement getting pending sync items
- **ServiceImplementations.cs:1040** - Implement camera availability check
- **ServiceImplementations.cs:1046** - Implement photo capture
- **ServiceImplementations.cs:1052** - Implement photo selection
- **ServiceImplementations.cs:1058** - Implement image resizing

### **Sync Service**
- **SyncServiceImplementation.cs:267** - Implement pending audits count
- **SyncServiceImplementation.cs:271** - Store and retrieve last sync error

## 📋 **IMPLEMENTATION PRIORITY**

### **Phase 1 - Core API Services (Immediate)**
1. Complete AuthController user management methods
2. Implement AuditsController service methods
3. Implement domain event handling
4. Complete AuditService finding creation

### **Phase 2 - Background Services (High)**
1. Implement AD sync functionality in SchedulerWorker
2. Implement recurring audit generation
3. Complete domain event publishing mechanism

### **Phase 3 - Web Application Features (Medium)**
1. Complete finding and corrective action management
2. Implement user and organization management features
3. Complete template management functionality
4. Add navigation and modal implementations

### **Phase 4 - PWA Enhancements (Medium)**
1. Implement comprehensive sync functionality
2. Complete camera and photo handling
3. Add export and share capabilities
4. Implement offline error handling

### **Phase 5 - Polish & UX (Low)**
1. Add proper error messages and user feedback
2. Implement dynamic data loading
3. Complete navigation flows
4. Add confirmation dialogs

## 🔍 **NOTES**

- Many TODO items are related to the recent MediatR removal and need service pattern implementation
- PWA sync functionality is partially implemented but needs completion
- Error handling and user feedback systems need comprehensive implementation
- Database normalization for Factory processes should be considered for future versions
