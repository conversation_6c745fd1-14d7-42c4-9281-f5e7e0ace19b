using HWSAuditPlatform.Application.Users.DTOs;
using HWSAuditPlatform.Domain.Entities.Users;

namespace HWSAuditPlatform.Application.Users.Extensions;

/// <summary>
/// Extension methods for mapping User entities to DTOs
/// </summary>
public static class UserMappingExtensions
{
    /// <summary>
    /// Maps a User entity to UserDto
    /// </summary>
    public static UserDto ToDto(this User user)
    {
        return new UserDto
        {
            Id = user.Id,
            Username = user.Username,
            FirstName = user.FirstName,
            LastName = user.LastName,
            Email = user.Email,
            Role = user.Role.RoleName,
            FactoryName = user.Factory?.FactoryName,
            IsActive = user.IsActive,
            CreatedAt = user.CreatedAt,
            UpdatedAt = user.UpdatedAt,
            RecordVersion = user.RecordVersion
        };
    }

    /// <summary>
    /// Maps a User entity to UserSummaryDto
    /// </summary>
    public static UserSummaryDto ToSummaryDto(this User user)
    {
        return new UserSummaryDto
        {
            Id = user.Id,
            Username = user.Username,
            FullName = $"{user.FirstName} {user.LastName}".Trim(),
            Role = user.Role.RoleName,
            FactoryName = user.Factory?.FactoryName,
            IsActive = user.IsActive
        };
    }

    /// <summary>
    /// Maps a Role entity to RoleDto
    /// </summary>
    public static RoleDto ToDto(this Role role)
    {
        return new RoleDto
        {
            Id = role.Id,
            RoleName = role.RoleName,
            Description = role.Description,
            IsActive = true, // Role entity doesn't have IsActive, assume active
            CreatedAt = role.CreatedAt,
            UpdatedAt = role.UpdatedAt
        };
    }

    /// <summary>
    /// Maps a UserGroup entity to UserGroupDto
    /// </summary>
    public static UserGroupDto ToDto(this UserGroup userGroup)
    {
        return new UserGroupDto
        {
            Id = userGroup.Id,
            GroupName = userGroup.GroupName,
            Description = userGroup.Description,
            CreatedBy = userGroup.CreatedByUserId, // Use CreatedByUserId from AuditableEntity
            CreatedByUserName = userGroup.CreatedByUser != null
                ? $"{userGroup.CreatedByUser.FirstName} {userGroup.CreatedByUser.LastName}".Trim()
                : null,
            CreatedAt = userGroup.CreatedAt,
            UpdatedAt = userGroup.UpdatedAt,
            CreatedByUserId = userGroup.CreatedByUserId,
            UpdatedByUserId = userGroup.UpdatedByUserId,
            RecordVersion = userGroup.RecordVersion,
            AdObjectGuid = userGroup.AdObjectGuid,
            IsAdSynced = userGroup.IsAdSynced,
            IsActive = true, // UserGroup entity doesn't have IsActive, assume active
            Members = userGroup.UserGroupMembers?.Select(ugm => ugm.User.ToSummaryDto()).ToList() ?? new List<UserSummaryDto>()
        };
    }

    /// <summary>
    /// Maps a UserGroup entity to UserGroupSummaryDto
    /// </summary>
    public static UserGroupSummaryDto ToSummaryDto(this UserGroup userGroup)
    {
        return new UserGroupSummaryDto
        {
            Id = userGroup.Id,
            GroupName = userGroup.GroupName,
            Description = userGroup.Description,
            MemberCount = userGroup.UserGroupMembers?.Count ?? 0,
            IsActive = true // UserGroup entity doesn't have IsActive, assume active
        };
    }

    /// <summary>
    /// Maps an AdGroupRoleMapping entity to AdGroupRoleMappingDto
    /// </summary>
    public static AdGroupRoleMappingDto ToDto(this AdGroupRoleMapping mapping)
    {
        return new AdGroupRoleMappingDto
        {
            Id = mapping.Id,
            AdGroupName = mapping.AdGroupName,
            RoleId = mapping.RoleId,
            RoleName = mapping.Role.RoleName,
            IsActive = mapping.IsActive,
            CreatedAt = mapping.CreatedAt,
            UpdatedAt = mapping.UpdatedAt
        };
    }

    /// <summary>
    /// Maps a collection of User entities to UserSummaryDto collection
    /// </summary>
    public static IEnumerable<UserSummaryDto> ToSummaryDtos(this IEnumerable<User> users)
    {
        return users.Select(u => u.ToSummaryDto());
    }

    /// <summary>
    /// Maps a collection of UserGroup entities to UserGroupSummaryDto collection
    /// </summary>
    public static IEnumerable<UserGroupSummaryDto> ToSummaryDtos(this IEnumerable<UserGroup> userGroups)
    {
        return userGroups.Select(ug => ug.ToSummaryDto());
    }

    /// <summary>
    /// Maps a collection of AdGroupRoleMapping entities to AdGroupRoleMappingDto collection
    /// </summary>
    public static IEnumerable<AdGroupRoleMappingDto> ToDtos(this IEnumerable<AdGroupRoleMapping> mappings)
    {
        return mappings.Select(m => m.ToDto());
    }



    /// <summary>
    /// Maps a collection of User entities to UserDto collection
    /// </summary>
    public static IEnumerable<UserDto> ToDtos(this IEnumerable<User> users)
    {
        return users.Select(u => u.ToDto());
    }
}
