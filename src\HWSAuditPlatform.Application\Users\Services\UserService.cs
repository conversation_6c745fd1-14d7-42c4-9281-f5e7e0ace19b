using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Linq.Expressions;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Application.Users.DTOs;
using HWSAuditPlatform.Application.Users.Extensions;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Domain.Entities.Users;
using HWSAuditPlatform.Domain.Enums;
using HWSAuditPlatform.Domain.Common;

namespace HWSAuditPlatform.Application.Users.Services;

/// <summary>
/// Service implementation for user management operations
/// </summary>
public class UserService : BaseService<UserService>, IUserService
{
    private readonly IAdRoleMappingService _adRoleMappingService;
    private readonly IActiveDirectoryService _activeDirectoryService;

    public UserService(
        IApplicationDbContext context,
        ICurrentUserService currentUserService,
        ILogger<UserService> logger,
        IAdRoleMappingService adRoleMappingService,
        IActiveDirectoryService activeDirectoryService)
        : base(context, currentUserService, logger)
    {
        _adRoleMappingService = adRoleMappingService;
        _activeDirectoryService = activeDirectoryService;
    }

    public async Task<PaginatedResult<UserSummaryDto>> GetUsersAsync(GetUsersRequest request, CancellationToken cancellationToken = default)
    {
        LogOperationStart(nameof(GetUsersAsync), request);

        try
        {
            var query = Context.Users
                .Include(u => u.Role)
                .Include(u => u.Factory)
                .AsQueryable();

            // Apply filters
            if (!string.IsNullOrEmpty(request.SearchTerm))
            {
                var searchPattern = $"%{request.SearchTerm}%";
                query = query.Where(u =>
                    EF.Functions.Like(u.Username, searchPattern) ||
                    (u.FirstName != null && EF.Functions.Like(u.FirstName, searchPattern)) ||
                    (u.LastName != null && EF.Functions.Like(u.LastName, searchPattern)) ||
                    EF.Functions.Like(u.Email, searchPattern));
            }

            if (request.Role.HasValue)
            {
                query = query.Where(u => u.Role.RoleName == request.Role.Value);
            }

            if (request.IsActive.HasValue)
            {
                query = query.Where(u => u.IsActive == request.IsActive.Value);
            }

            if (request.FactoryId.HasValue)
            {
                query = query.Where(u => u.FactoryId == request.FactoryId.Value);
            }

            // Apply sorting
            query = ApplySorting(query, request.SortBy, request.SortDescending);

            // Get total count
            var totalCount = await query.CountAsync(cancellationToken);

            // Apply pagination and map to DTOs
            var users = await query
                .Skip((request.PageNumber - 1) * request.PageSize)
                .Take(request.PageSize)
                .ToListAsync(cancellationToken);

            var userDtos = users.ToSummaryDtos().ToList();

            var result = PaginatedResult<UserSummaryDto>.Create(userDtos, totalCount, request.PageNumber, request.PageSize);
            
            LogOperationComplete(nameof(GetUsersAsync), new { Count = userDtos.Count, TotalCount = totalCount });
            return result;
        }
        catch (Exception ex)
        {
            LogOperationError(nameof(GetUsersAsync), ex);
            throw;
        }
    }

    public async Task<UserDto> GetUserAsync(string id, CancellationToken cancellationToken = default)
    {
        LogOperationStart(nameof(GetUserAsync), new { Id = id });

        try
        {
            var user = await Context.Users
                .Include(u => u.Role)
                .Include(u => u.Factory)
                .FirstOrDefaultAsync(u => u.Id == id, cancellationToken);

            if (user == null)
            {
                throw new NotFoundException(nameof(User), id);
            }

            var result = user.ToDto();
            LogOperationComplete(nameof(GetUserAsync));
            return result;
        }
        catch (Exception ex)
        {
            LogOperationError(nameof(GetUserAsync), ex);
            throw;
        }
    }

    public async Task<string> CreateUserAsync(CreateUserRequest request, CancellationToken cancellationToken = default)
    {
        LogOperationStart(nameof(CreateUserAsync), request);

        try
        {
            await ValidateCreateUserRequestAsync(request, cancellationToken);

            // Get the role entity
            var role = await Context.Roles
                .FirstOrDefaultAsync(r => r.RoleName == request.Role, cancellationToken);

            if (role == null)
            {
                throw new ValidationException($"Role '{request.Role}' not found");
            }

            var user = new User
            {
                Id = CuidGenerator.Generate(),
                Username = request.Username,
                FirstName = request.FirstName,
                LastName = request.LastName,
                Email = request.Email,
                RoleId = role.Id,
                FactoryId = request.FactoryId,
                IsActive = request.IsActive,
                CreatedByUserId = CurrentUserService.UserId,
                CreatedAt = DateTime.UtcNow,
                UpdatedByUserId = CurrentUserService.UserId,
                UpdatedAt = DateTime.UtcNow
            };

            Context.Users.Add(user);
            await Context.SaveChangesAsync(cancellationToken);

            LogOperationComplete(nameof(CreateUserAsync), new { UserId = user.Id });
            return user.Id;
        }
        catch (Exception ex)
        {
            LogOperationError(nameof(CreateUserAsync), ex);
            throw;
        }
    }

    public async Task UpdateUserAsync(UpdateUserRequest request, CancellationToken cancellationToken = default)
    {
        LogOperationStart(nameof(UpdateUserAsync), request);

        try
        {
            var user = await Context.Users
                .FirstOrDefaultAsync(u => u.Id == request.Id, cancellationToken);

            if (user == null)
            {
                throw new NotFoundException(nameof(User), request.Id);
            }

            // Check for concurrency conflicts
            if (user.RecordVersion != request.RecordVersion)
            {
                throw new InvalidOperationException("The user has been modified by another user. Please refresh and try again.");
            }

            // Get the role entity
            var role = await Context.Roles
                .FirstOrDefaultAsync(r => r.RoleName == request.Role, cancellationToken);

            if (role == null)
            {
                throw new ValidationException($"Role '{request.Role}' not found");
            }

            // Update user properties
            user.FirstName = request.FirstName;
            user.LastName = request.LastName;
            user.Email = request.Email;
            user.RoleId = role.Id;
            user.FactoryId = request.FactoryId;
            user.IsActive = request.IsActive;
            user.UpdatedByUserId = CurrentUserService.UserId;
            user.UpdatedAt = DateTime.UtcNow;
            user.RecordVersion++;

            await Context.SaveChangesAsync(cancellationToken);

            LogOperationComplete(nameof(UpdateUserAsync));
        }
        catch (Exception ex)
        {
            LogOperationError(nameof(UpdateUserAsync), ex);
            throw;
        }
    }

    public async Task DeleteUserAsync(string id, CancellationToken cancellationToken = default)
    {
        LogOperationStart(nameof(DeleteUserAsync), new { Id = id });

        try
        {
            var user = await Context.Users
                .FirstOrDefaultAsync(u => u.Id == id, cancellationToken);

            if (user == null)
            {
                throw new NotFoundException(nameof(User), id);
            }

            // Soft delete by setting IsActive to false
            user.IsActive = false;
            user.UpdatedByUserId = CurrentUserService.UserId;
            user.UpdatedAt = DateTime.UtcNow;
            user.RecordVersion++;

            await Context.SaveChangesAsync(cancellationToken);

            LogOperationComplete(nameof(DeleteUserAsync));
        }
        catch (Exception ex)
        {
            LogOperationError(nameof(DeleteUserAsync), ex);
            throw;
        }
    }

    public async Task<List<RoleDto>> GetRolesAsync(CancellationToken cancellationToken = default)
    {
        LogOperationStart(nameof(GetRolesAsync));

        try
        {
            var roles = await Context.Roles
                .Where(r => r.IsActive)
                .OrderBy(r => r.RoleName)
                .ToListAsync(cancellationToken);

            var result = roles.Select(r => r.ToDto()).ToList();
            
            LogOperationComplete(nameof(GetRolesAsync), new { Count = result.Count });
            return result;
        }
        catch (Exception ex)
        {
            LogOperationError(nameof(GetRolesAsync), ex);
            throw;
        }
    }

    private static IQueryable<User> ApplySorting(IQueryable<User> query, string? sortBy, bool sortDescending)
    {
        Expression<Func<User, object>> keySelector = sortBy?.ToLower() switch
        {
            "firstname" => u => u.FirstName ?? string.Empty,
            "lastname" => u => u.LastName ?? string.Empty,
            "email" => u => u.Email,
            "role" => u => u.Role.RoleName,
            "isactive" => u => u.IsActive,
            "createdat" => u => u.CreatedAt,
            _ => u => u.Username
        };

        return sortDescending 
            ? query.OrderByDescending(keySelector)
            : query.OrderBy(keySelector);
    }

    private async Task ValidateCreateUserRequestAsync(CreateUserRequest request, CancellationToken cancellationToken)
    {
        // Check if username already exists
        var existingUser = await Context.Users
            .FirstOrDefaultAsync(u => u.Username == request.Username, cancellationToken);

        if (existingUser != null)
        {
            throw new ValidationException($"Username '{request.Username}' already exists");
        }

        // Additional validation can be added here
    }

    // Placeholder implementations for other methods - will be implemented in next chunk
    public Task<SyncUsersFromAdResult> SyncUsersFromAdAsync(SyncUsersFromAdRequest request, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException("Will be implemented in next update");
    }

    public Task<PaginatedResult<UserGroupSummaryDto>> GetUserGroupsAsync(GetUserGroupsRequest request, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException("Will be implemented in next update");
    }

    public Task<UserGroupDto> GetUserGroupAsync(int id, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException("Will be implemented in next update");
    }

    public Task<int> CreateUserGroupAsync(CreateUserGroupRequest request, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException("Will be implemented in next update");
    }

    public Task UpdateUserGroupAsync(UpdateUserGroupRequest request, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException("Will be implemented in next update");
    }

    public Task DeleteUserGroupAsync(int id, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException("Will be implemented in next update");
    }

    public Task<List<AdGroupRoleMappingDto>> GetAdGroupRoleMappingsAsync(CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException("Will be implemented in next update");
    }

    public Task<int> CreateAdGroupRoleMappingAsync(CreateAdGroupRoleMappingRequest request, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException("Will be implemented in next update");
    }

    public Task UpdateAdGroupRoleMappingAsync(UpdateAdGroupRoleMappingRequest request, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException("Will be implemented in next update");
    }

    public Task DeleteAdGroupRoleMappingAsync(int id, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException("Will be implemented in next update");
    }

    public Task<PaginatedResult<ProcessOwnerAssignmentDto>> GetProcessOwnerAssignmentsAsync(GetProcessOwnerAssignmentsRequest request, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException("Will be implemented in next update");
    }

    public Task<int> CreateProcessOwnerAssignmentAsync(CreateProcessOwnerAssignmentRequest request, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException("Will be implemented in next update");
    }

    public Task UpdateProcessOwnerAssignmentAsync(UpdateProcessOwnerAssignmentRequest request, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException("Will be implemented in next update");
    }

    public Task DeleteProcessOwnerAssignmentAsync(int id, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException("Will be implemented in next update");
    }

    public Task<List<UserSummaryDto>> SearchUsersAsync(string searchTerm, int maxResults = 5, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException("Will be implemented in next update");
    }
}
