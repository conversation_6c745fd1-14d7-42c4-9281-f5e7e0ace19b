using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Linq.Expressions;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Application.Users.DTOs;
using HWSAuditPlatform.Application.Users.Extensions;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Application.Users.Commands.SyncUsersFromAd;
using HWSAuditPlatform.Domain.Entities.Users;
using HWSAuditPlatform.Domain.Enums;
using HWSAuditPlatform.Domain.Common;

namespace HWSAuditPlatform.Application.Users.Services;

/// <summary>
/// Service implementation for user management operations
/// </summary>
public class UserService : BaseService<UserService>, IUserService
{
    private readonly IAdRoleMappingService _adRoleMappingService;
    private readonly IActiveDirectoryService _activeDirectoryService;

    public UserService(
        IApplicationDbContext context,
        ICurrentUserService currentUserService,
        ILogger<UserService> logger,
        IAdRoleMappingService adRoleMappingService,
        IActiveDirectoryService activeDirectoryService)
        : base(context, currentUserService, logger)
    {
        _adRoleMappingService = adRoleMappingService;
        _activeDirectoryService = activeDirectoryService;
    }

    public async Task<PaginatedResult<UserSummaryDto>> GetUsersAsync(GetUsersRequest request, CancellationToken cancellationToken = default)
    {
        LogOperationStart(nameof(GetUsersAsync), request);

        try
        {
            var query = Context.Users
                .Include(u => u.Role)
                .Include(u => u.Factory)
                .AsQueryable();

            // Apply filters
            if (!string.IsNullOrEmpty(request.SearchTerm))
            {
                var searchPattern = $"%{request.SearchTerm}%";
                query = query.Where(u =>
                    EF.Functions.Like(u.Username, searchPattern) ||
                    (u.FirstName != null && EF.Functions.Like(u.FirstName, searchPattern)) ||
                    (u.LastName != null && EF.Functions.Like(u.LastName, searchPattern)) ||
                    EF.Functions.Like(u.Email, searchPattern));
            }

            if (request.Role.HasValue)
            {
                query = query.Where(u => u.Role.RoleName == request.Role.Value);
            }

            if (request.IsActive.HasValue)
            {
                query = query.Where(u => u.IsActive == request.IsActive.Value);
            }

            if (request.FactoryId.HasValue)
            {
                query = query.Where(u => u.FactoryId == request.FactoryId.Value);
            }

            // Apply sorting
            query = ApplySorting(query, request.SortBy, request.SortDescending);

            // Get total count
            var totalCount = await query.CountAsync(cancellationToken);

            // Apply pagination and map to DTOs
            var users = await query
                .Skip((request.PageNumber - 1) * request.PageSize)
                .Take(request.PageSize)
                .ToListAsync(cancellationToken);

            var userDtos = users.ToSummaryDtos().ToList();

            var result = PaginatedResult<UserSummaryDto>.Create(userDtos, totalCount, request.PageNumber, request.PageSize);
            
            LogOperationComplete(nameof(GetUsersAsync), new { Count = userDtos.Count, TotalCount = totalCount });
            return result;
        }
        catch (Exception ex)
        {
            LogOperationError(nameof(GetUsersAsync), ex);
            throw;
        }
    }

    public async Task<UserDto> GetUserAsync(string id, CancellationToken cancellationToken = default)
    {
        LogOperationStart(nameof(GetUserAsync), new { Id = id });

        try
        {
            var user = await Context.Users
                .Include(u => u.Role)
                .Include(u => u.Factory)
                .FirstOrDefaultAsync(u => u.Id == id, cancellationToken);

            if (user == null)
            {
                throw new NotFoundException(nameof(User), id);
            }

            var result = user.ToDto();
            LogOperationComplete(nameof(GetUserAsync));
            return result;
        }
        catch (Exception ex)
        {
            LogOperationError(nameof(GetUserAsync), ex);
            throw;
        }
    }

    public async Task<string> CreateUserAsync(CreateUserRequest request, CancellationToken cancellationToken = default)
    {
        LogOperationStart(nameof(CreateUserAsync), request);

        try
        {
            await ValidateCreateUserRequestAsync(request, cancellationToken);

            // Get the role entity
            var role = await Context.Roles
                .FirstOrDefaultAsync(r => r.RoleName == request.Role, cancellationToken);

            if (role == null)
            {
                throw new ValidationException($"Role '{request.Role}' not found");
            }

            var user = new User
            {
                Id = CuidGenerator.Generate(),
                Username = request.Username,
                FirstName = request.FirstName,
                LastName = request.LastName,
                Email = request.Email,
                RoleId = role.Id,
                FactoryId = request.FactoryId,
                IsActive = request.IsActive,
                CreatedByUserId = CurrentUserService.UserId,
                CreatedAt = DateTime.UtcNow,
                UpdatedByUserId = CurrentUserService.UserId,
                UpdatedAt = DateTime.UtcNow
            };

            Context.Users.Add(user);
            await Context.SaveChangesAsync(cancellationToken);

            LogOperationComplete(nameof(CreateUserAsync), new { UserId = user.Id });
            return user.Id;
        }
        catch (Exception ex)
        {
            LogOperationError(nameof(CreateUserAsync), ex);
            throw;
        }
    }

    public async Task UpdateUserAsync(UpdateUserRequest request, CancellationToken cancellationToken = default)
    {
        LogOperationStart(nameof(UpdateUserAsync), request);

        try
        {
            var user = await Context.Users
                .FirstOrDefaultAsync(u => u.Id == request.Id, cancellationToken);

            if (user == null)
            {
                throw new NotFoundException(nameof(User), request.Id);
            }

            // Check for concurrency conflicts
            if (user.RecordVersion != request.RecordVersion)
            {
                throw new InvalidOperationException("The user has been modified by another user. Please refresh and try again.");
            }

            // Get the role entity
            var role = await Context.Roles
                .FirstOrDefaultAsync(r => r.RoleName == request.Role, cancellationToken);

            if (role == null)
            {
                throw new ValidationException($"Role '{request.Role}' not found");
            }

            // Update user properties
            user.FirstName = request.FirstName;
            user.LastName = request.LastName;
            user.Email = request.Email;
            user.RoleId = role.Id;
            user.FactoryId = request.FactoryId;
            user.IsActive = request.IsActive;
            user.UpdatedByUserId = CurrentUserService.UserId;
            user.UpdatedAt = DateTime.UtcNow;
            user.RecordVersion++;

            await Context.SaveChangesAsync(cancellationToken);

            LogOperationComplete(nameof(UpdateUserAsync));
        }
        catch (Exception ex)
        {
            LogOperationError(nameof(UpdateUserAsync), ex);
            throw;
        }
    }

    public async Task DeleteUserAsync(string id, CancellationToken cancellationToken = default)
    {
        LogOperationStart(nameof(DeleteUserAsync), new { Id = id });

        try
        {
            var user = await Context.Users
                .FirstOrDefaultAsync(u => u.Id == id, cancellationToken);

            if (user == null)
            {
                throw new NotFoundException(nameof(User), id);
            }

            // Soft delete by setting IsActive to false
            user.IsActive = false;
            user.UpdatedByUserId = CurrentUserService.UserId;
            user.UpdatedAt = DateTime.UtcNow;
            user.RecordVersion++;

            await Context.SaveChangesAsync(cancellationToken);

            LogOperationComplete(nameof(DeleteUserAsync));
        }
        catch (Exception ex)
        {
            LogOperationError(nameof(DeleteUserAsync), ex);
            throw;
        }
    }

    public async Task<List<RoleDto>> GetRolesAsync(CancellationToken cancellationToken = default)
    {
        LogOperationStart(nameof(GetRolesAsync));

        try
        {
            var roles = await Context.Roles
                .Where(r => r.IsActive)
                .OrderBy(r => r.RoleName)
                .ToListAsync(cancellationToken);

            var result = roles.Select(r => r.ToDto()).ToList();
            
            LogOperationComplete(nameof(GetRolesAsync), new { Count = result.Count });
            return result;
        }
        catch (Exception ex)
        {
            LogOperationError(nameof(GetRolesAsync), ex);
            throw;
        }
    }

    private static IQueryable<User> ApplySorting(IQueryable<User> query, string? sortBy, bool sortDescending)
    {
        Expression<Func<User, object>> keySelector = sortBy?.ToLower() switch
        {
            "firstname" => u => u.FirstName ?? string.Empty,
            "lastname" => u => u.LastName ?? string.Empty,
            "email" => u => u.Email,
            "role" => u => u.Role.RoleName,
            "isactive" => u => u.IsActive,
            "createdat" => u => u.CreatedAt,
            _ => u => u.Username
        };

        return sortDescending 
            ? query.OrderByDescending(keySelector)
            : query.OrderBy(keySelector);
    }

    private async Task ValidateCreateUserRequestAsync(CreateUserRequest request, CancellationToken cancellationToken)
    {
        // Check if username already exists
        var existingUser = await Context.Users
            .FirstOrDefaultAsync(u => u.Username == request.Username, cancellationToken);

        if (existingUser != null)
        {
            throw new ValidationException($"Username '{request.Username}' already exists");
        }

        // Additional validation can be added here
    }

    public async Task<List<UserSummaryDto>> SearchUsersAsync(string searchTerm, int maxResults = 5, CancellationToken cancellationToken = default)
    {
        LogOperationStart(nameof(SearchUsersAsync), new { SearchTerm = searchTerm, MaxResults = maxResults });

        try
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                return new List<UserSummaryDto>();
            }

            var searchPattern = $"%{searchTerm}%";
            var users = await Context.Users
                .Include(u => u.Role)
                .Include(u => u.Factory)
                .Where(u => u.IsActive && (
                    EF.Functions.Like(u.Username, searchPattern) ||
                    EF.Functions.Like(u.FirstName, searchPattern) ||
                    EF.Functions.Like(u.LastName, searchPattern) ||
                    (u.Email != null && EF.Functions.Like(u.Email, searchPattern))
                ))
                .OrderBy(u => u.Username)
                .Take(maxResults)
                .ToListAsync(cancellationToken);

            var result = users.ToSummaryDtos().ToList();
            LogOperationComplete(nameof(SearchUsersAsync), new { Count = result.Count });
            return result;
        }
        catch (Exception ex)
        {
            LogOperationError(nameof(SearchUsersAsync), ex);
            throw;
        }
    }

    public async Task<SyncUsersFromAdResult> SyncUsersFromAdAsync(SyncUsersFromAdRequest request, CancellationToken cancellationToken = default)
    {
        LogOperationStart(nameof(SyncUsersFromAdAsync), request);

        try
        {
            var result = new SyncUsersFromAdResult
            {
                SyncStartTime = DateTime.UtcNow,
                WasDryRun = request.DryRun
            };

            // Get all AD users
            var adUsers = await _activeDirectoryService.GetUsersAsync(cancellationToken);
            var adUsersList = adUsers.ToList();
            result.TotalAdUsers = adUsersList.Count;

            // Get all existing users with AD sync data
            var dbUsers = await Context.Users
                .Where(u => !string.IsNullOrEmpty(u.AdObjectGuid))
                .ToListAsync(cancellationToken);
            result.TotalDbUsers = dbUsers.Count;

            var dbUsersByAdGuid = dbUsers.ToDictionary(u => u.AdObjectGuid, u => u);
            var adUsersByGuid = adUsersList.ToDictionary(u => u.ObjectGuid, u => u);

            // Process AD users (create new or update existing)
            foreach (var adUser in adUsersList)
            {
                try
                {
                    if (dbUsersByAdGuid.TryGetValue(adUser.ObjectGuid, out var existingUser))
                    {
                        // Update existing user
                        if (await UpdateUserFromAdAsync(existingUser, adUser, request.DryRun, cancellationToken))
                        {
                            result.UsersUpdated++;
                        }
                    }
                    else
                    {
                        // Create new user
                        if (await CreateUserFromAdAsync(adUser, request.DryRun, cancellationToken))
                        {
                            result.UsersCreated++;
                        }
                    }
                }
                catch (Exception ex)
                {
                    var error = $"Error processing AD user {adUser.Username}: {ex.Message}";
                    result.Errors.Add(error);
                    Logger.LogError(ex, "Error processing AD user {Username}", adUser.Username);
                }
            }

            // Disable users that are no longer in AD
            foreach (var dbUser in dbUsers)
            {
                if (!adUsersByGuid.ContainsKey(dbUser.AdObjectGuid) && dbUser.IsActive)
                {
                    try
                    {
                        if (!request.DryRun)
                        {
                            dbUser.IsActive = false;
                            dbUser.UpdatedByUserId = CurrentUserService.UserId;
                            dbUser.UpdatedAt = DateTime.UtcNow;
                        }
                        result.UsersDisabled++;
                    }
                    catch (Exception ex)
                    {
                        var error = $"Error disabling user {dbUser.Username}: {ex.Message}";
                        result.Errors.Add(error);
                        Logger.LogError(ex, "Error disabling user {Username}", dbUser.Username);
                    }
                }
            }

            // Save changes if not a dry run
            if (!request.DryRun)
            {
                await Context.SaveChangesAsync(cancellationToken);
            }

            result.SyncEndTime = DateTime.UtcNow;
            LogOperationComplete(nameof(SyncUsersFromAdAsync), result);
            return result;
        }
        catch (Exception ex)
        {
            LogOperationError(nameof(SyncUsersFromAdAsync), ex);
            throw;
        }
    }

    public async Task<PaginatedResult<UserGroupSummaryDto>> GetUserGroupsAsync(GetUserGroupsRequest request, CancellationToken cancellationToken = default)
    {
        LogOperationStart(nameof(GetUserGroupsAsync), request);

        try
        {
            var query = Context.UserGroups
                .Include(ug => ug.UserGroupMembers)
                .AsQueryable();

            // Apply search filter
            if (!string.IsNullOrWhiteSpace(request.SearchTerm))
            {
                var searchPattern = $"%{request.SearchTerm}%";
                query = query.Where(ug =>
                    EF.Functions.Like(ug.GroupName, searchPattern) ||
                    (ug.Description != null && EF.Functions.Like(ug.Description, searchPattern)));
            }

            // Apply active filter
            if (request.IsActive.HasValue)
            {
                query = query.Where(ug => ug.IsActive == request.IsActive.Value);
            }

            // Apply sorting
            query = request.SortBy?.ToLower() switch
            {
                "groupname" => request.SortDescending ? query.OrderByDescending(ug => ug.GroupName) : query.OrderBy(ug => ug.GroupName),
                "membercount" => request.SortDescending ? query.OrderByDescending(ug => ug.UserGroupMembers.Count) : query.OrderBy(ug => ug.UserGroupMembers.Count),
                "createdat" => request.SortDescending ? query.OrderByDescending(ug => ug.CreatedAt) : query.OrderBy(ug => ug.CreatedAt),
                _ => query.OrderBy(ug => ug.GroupName)
            };

            var totalCount = await query.CountAsync(cancellationToken);
            var userGroups = await query
                .Skip((request.PageNumber - 1) * request.PageSize)
                .Take(request.PageSize)
                .ToListAsync(cancellationToken);

            var userGroupDtos = userGroups.ToSummaryDtos().ToList();
            var result = PaginatedResult<UserGroupSummaryDto>.Create(userGroupDtos, totalCount, request.PageNumber, request.PageSize);

            LogOperationComplete(nameof(GetUserGroupsAsync), new { Count = result.Items.Count, TotalCount = result.TotalCount });
            return result;
        }
        catch (Exception ex)
        {
            LogOperationError(nameof(GetUserGroupsAsync), ex);
            throw;
        }
    }

    public async Task<UserGroupDto> GetUserGroupAsync(int id, CancellationToken cancellationToken = default)
    {
        LogOperationStart(nameof(GetUserGroupAsync), new { Id = id });

        try
        {
            var userGroup = await Context.UserGroups
                .Include(ug => ug.UserGroupMembers)
                    .ThenInclude(ugm => ugm.User)
                        .ThenInclude(u => u.Role)
                .Include(ug => ug.UserGroupMembers)
                    .ThenInclude(ugm => ugm.User)
                        .ThenInclude(u => u.Factory)
                .Include(ug => ug.CreatedByUser)
                .FirstOrDefaultAsync(ug => ug.Id == id.ToString(), cancellationToken);

            if (userGroup == null)
            {
                throw new NotFoundException($"User group with ID {id} not found");
            }

            var result = userGroup.ToDto();
            LogOperationComplete(nameof(GetUserGroupAsync), result);
            return result;
        }
        catch (Exception ex)
        {
            LogOperationError(nameof(GetUserGroupAsync), ex);
            throw;
        }
    }

    public async Task<int> CreateUserGroupAsync(CreateUserGroupRequest request, CancellationToken cancellationToken = default)
    {
        LogOperationStart(nameof(CreateUserGroupAsync), request);

        try
        {
            await ValidateCreateUserGroupRequestAsync(request, cancellationToken);

            var userGroupId = CuidGenerator.Generate();
            var userGroup = UserGroup.Create(
                id: userGroupId,
                groupName: request.GroupName,
                description: request.Description,
                createdByUserId: CurrentUserService.UserId);

            await Context.UserGroups.AddAsync(userGroup, cancellationToken);

            // Add members if specified
            if (request.MemberIds.Any())
            {
                foreach (var memberId in request.MemberIds)
                {
                    var member = UserGroupMember.Create(userGroupId, memberId);
                    await Context.UserGroupMembers.AddAsync(member, cancellationToken);
                }
            }

            await Context.SaveChangesAsync(cancellationToken);

            var result = int.Parse(userGroupId.Substring(1)); // Convert CUID to int for return
            LogOperationComplete(nameof(CreateUserGroupAsync), new { Id = result });
            return result;
        }
        catch (Exception ex)
        {
            LogOperationError(nameof(CreateUserGroupAsync), ex);
            throw;
        }
    }

    public async Task UpdateUserGroupAsync(UpdateUserGroupRequest request, CancellationToken cancellationToken = default)
    {
        LogOperationStart(nameof(UpdateUserGroupAsync), request);

        try
        {
            var userGroup = await Context.UserGroups
                .Include(ug => ug.UserGroupMembers)
                .FirstOrDefaultAsync(ug => ug.Id == request.Id.ToString(), cancellationToken);

            if (userGroup == null)
            {
                throw new NotFoundException($"User group with ID {request.Id} not found");
            }

            await ValidateUpdateUserGroupRequestAsync(request, userGroup, cancellationToken);

            userGroup.Update(
                groupName: request.GroupName,
                description: request.Description,
                updatedByUserId: CurrentUserService.UserId);

            // Update members
            var existingMemberIds = userGroup.UserGroupMembers.Select(ugm => ugm.UserId).ToHashSet();
            var newMemberIds = request.MemberIds.ToHashSet();

            // Remove members not in the new list
            var membersToRemove = userGroup.UserGroupMembers
                .Where(ugm => !newMemberIds.Contains(ugm.UserId))
                .ToList();

            foreach (var member in membersToRemove)
            {
                Context.UserGroupMembers.Remove(member);
            }

            // Add new members
            var membersToAdd = newMemberIds
                .Where(id => !existingMemberIds.Contains(id))
                .ToList();

            foreach (var memberId in membersToAdd)
            {
                var member = UserGroupMember.Create(userGroup.Id, memberId);
                await Context.UserGroupMembers.AddAsync(member, cancellationToken);
            }

            await Context.SaveChangesAsync(cancellationToken);
            LogOperationComplete(nameof(UpdateUserGroupAsync));
        }
        catch (Exception ex)
        {
            LogOperationError(nameof(UpdateUserGroupAsync), ex);
            throw;
        }
    }

    public async Task DeleteUserGroupAsync(int id, CancellationToken cancellationToken = default)
    {
        LogOperationStart(nameof(DeleteUserGroupAsync), new { Id = id });

        try
        {
            var userGroup = await Context.UserGroups
                .Include(ug => ug.UserGroupMembers)
                .FirstOrDefaultAsync(ug => ug.Id == id.ToString(), cancellationToken);

            if (userGroup == null)
            {
                throw new NotFoundException($"User group with ID {id} not found");
            }

            // Remove all members first
            Context.UserGroupMembers.RemoveRange(userGroup.UserGroupMembers);

            // Remove the user group
            Context.UserGroups.Remove(userGroup);

            await Context.SaveChangesAsync(cancellationToken);
            LogOperationComplete(nameof(DeleteUserGroupAsync));
        }
        catch (Exception ex)
        {
            LogOperationError(nameof(DeleteUserGroupAsync), ex);
            throw;
        }
    }

    public async Task<List<AdGroupRoleMappingDto>> GetAdGroupRoleMappingsAsync(CancellationToken cancellationToken = default)
    {
        LogOperationStart(nameof(GetAdGroupRoleMappingsAsync));

        try
        {
            var mappings = await Context.AdGroupRoleMappings
                .Include(m => m.Role)
                .Where(m => m.IsActive)
                .OrderBy(m => m.AdGroupName)
                .ToListAsync(cancellationToken);

            var result = mappings.ToDtos().ToList();
            LogOperationComplete(nameof(GetAdGroupRoleMappingsAsync), new { Count = result.Count });
            return result;
        }
        catch (Exception ex)
        {
            LogOperationError(nameof(GetAdGroupRoleMappingsAsync), ex);
            throw;
        }
    }

    public async Task<int> CreateAdGroupRoleMappingAsync(CreateAdGroupRoleMappingRequest request, CancellationToken cancellationToken = default)
    {
        LogOperationStart(nameof(CreateAdGroupRoleMappingAsync), request);

        try
        {
            await ValidateCreateAdGroupRoleMappingRequestAsync(request, cancellationToken);

            var mapping = AdGroupRoleMapping.Create(
                adGroupName: request.AdGroupName,
                roleId: request.RoleId,
                createdByUserId: CurrentUserService.UserId);

            await Context.AdGroupRoleMappings.AddAsync(mapping, cancellationToken);
            await Context.SaveChangesAsync(cancellationToken);

            LogOperationComplete(nameof(CreateAdGroupRoleMappingAsync), new { Id = mapping.Id });
            return mapping.Id;
        }
        catch (Exception ex)
        {
            LogOperationError(nameof(CreateAdGroupRoleMappingAsync), ex);
            throw;
        }
    }

    public async Task UpdateAdGroupRoleMappingAsync(UpdateAdGroupRoleMappingRequest request, CancellationToken cancellationToken = default)
    {
        LogOperationStart(nameof(UpdateAdGroupRoleMappingAsync), request);

        try
        {
            var mapping = await Context.AdGroupRoleMappings
                .FirstOrDefaultAsync(m => m.Id == request.Id, cancellationToken);

            if (mapping == null)
            {
                throw new NotFoundException($"AD group role mapping with ID {request.Id} not found");
            }

            await ValidateUpdateAdGroupRoleMappingRequestAsync(request, mapping, cancellationToken);

            mapping.Update(
                adGroupName: request.AdGroupName,
                roleId: request.RoleId,
                updatedByUserId: CurrentUserService.UserId);

            await Context.SaveChangesAsync(cancellationToken);
            LogOperationComplete(nameof(UpdateAdGroupRoleMappingAsync));
        }
        catch (Exception ex)
        {
            LogOperationError(nameof(UpdateAdGroupRoleMappingAsync), ex);
            throw;
        }
    }

    public async Task DeleteAdGroupRoleMappingAsync(int id, CancellationToken cancellationToken = default)
    {
        LogOperationStart(nameof(DeleteAdGroupRoleMappingAsync), new { Id = id });

        try
        {
            var mapping = await Context.AdGroupRoleMappings
                .FirstOrDefaultAsync(m => m.Id == id, cancellationToken);

            if (mapping == null)
            {
                throw new NotFoundException($"AD group role mapping with ID {id} not found");
            }

            Context.AdGroupRoleMappings.Remove(mapping);
            await Context.SaveChangesAsync(cancellationToken);
            LogOperationComplete(nameof(DeleteAdGroupRoleMappingAsync));
        }
        catch (Exception ex)
        {
            LogOperationError(nameof(DeleteAdGroupRoleMappingAsync), ex);
            throw;
        }
    }

    public async Task<PaginatedResult<ProcessOwnerAssignmentDto>> GetProcessOwnerAssignmentsAsync(GetProcessOwnerAssignmentsRequest request, CancellationToken cancellationToken = default)
    {
        LogOperationStart(nameof(GetProcessOwnerAssignmentsAsync), request);

        try
        {
            var query = Context.ProcessOwnerAssignments
                .Include(poa => poa.ProcessOwnerUser)
                .Include(poa => poa.Factory)
                .Include(poa => poa.Area)
                .Include(poa => poa.SubArea)
                .Include(poa => poa.AssignedByUser)
                .AsQueryable();

            // Apply filters
            if (!string.IsNullOrWhiteSpace(request.ProcessOwnerUserId))
            {
                query = query.Where(poa => poa.ProcessOwnerUserId == request.ProcessOwnerUserId);
            }

            if (request.FactoryId.HasValue)
            {
                query = query.Where(poa => poa.FactoryId == request.FactoryId.Value);
            }

            if (request.AreaId.HasValue)
            {
                query = query.Where(poa => poa.AreaId == request.AreaId.Value);
            }

            if (request.IsActive.HasValue)
            {
                query = query.Where(poa => poa.IsActive == request.IsActive.Value);
            }

            // Apply sorting
            query = request.SortBy?.ToLower() switch
            {
                "processowner" => request.SortDescending ? query.OrderByDescending(poa => poa.ProcessOwnerUser.Username) : query.OrderBy(poa => poa.ProcessOwnerUser.Username),
                "factory" => request.SortDescending ? query.OrderByDescending(poa => poa.Factory.FactoryName) : query.OrderBy(poa => poa.Factory.FactoryName),
                "area" => request.SortDescending ? query.OrderByDescending(poa => poa.Area.AreaName) : query.OrderBy(poa => poa.Area.AreaName),
                "createdat" => request.SortDescending ? query.OrderByDescending(poa => poa.CreatedAt) : query.OrderBy(poa => poa.CreatedAt),
                _ => query.OrderBy(poa => poa.ProcessOwnerUser.Username)
            };

            var totalCount = await query.CountAsync(cancellationToken);
            var assignments = await query
                .Skip((request.PageNumber - 1) * request.PageSize)
                .Take(request.PageSize)
                .ToListAsync(cancellationToken);

            var assignmentDtos = assignments.Select(poa => new ProcessOwnerAssignmentDto
            {
                Id = poa.Id,
                ProcessOwnerUserId = poa.ProcessOwnerUserId,
                ProcessOwnerUserName = poa.ProcessOwnerUser.Username,
                ProcessOwnerFullName = poa.ProcessOwnerUser.FullName,
                FactoryId = poa.FactoryId,
                FactoryName = poa.Factory?.FactoryName,
                AreaId = poa.AreaId,
                AreaName = poa.Area?.AreaName,
                SubAreaId = poa.SubAreaId,
                SubAreaName = poa.SubArea?.SubAreaName,
                AssignedByUserId = poa.AssignedByUserId,
                AssignedByUserName = poa.AssignedByUser.Username,
                AssignedByFullName = poa.AssignedByUser.FullName,
                IsActive = poa.IsActive,
                Description = poa.Description,
                ScopeDescription = GetScopeDescription(poa),
                CreatedAt = poa.CreatedAt,
                UpdatedAt = poa.UpdatedAt,
                RecordVersion = poa.RecordVersion
            }).ToList();

            var result = PaginatedResult<ProcessOwnerAssignmentDto>.Create(assignmentDtos, totalCount, request.PageNumber, request.PageSize);
            LogOperationComplete(nameof(GetProcessOwnerAssignmentsAsync), new { Count = result.Items.Count, TotalCount = result.TotalCount });
            return result;
        }
        catch (Exception ex)
        {
            LogOperationError(nameof(GetProcessOwnerAssignmentsAsync), ex);
            throw;
        }
    }

    public async Task<int> CreateProcessOwnerAssignmentAsync(CreateProcessOwnerAssignmentRequest request, CancellationToken cancellationToken = default)
    {
        LogOperationStart(nameof(CreateProcessOwnerAssignmentAsync), request);

        try
        {
            await ValidateCreateProcessOwnerAssignmentRequestAsync(request, cancellationToken);

            var assignment = ProcessOwnerAssignment.Create(
                processOwnerUserId: request.ProcessOwnerUserId,
                assignedByUserId: CurrentUserService.UserId,
                factoryId: request.FactoryId,
                areaId: request.AreaId,
                subAreaId: request.SubAreaId,
                description: request.Description);

            await Context.ProcessOwnerAssignments.AddAsync(assignment, cancellationToken);
            await Context.SaveChangesAsync(cancellationToken);

            LogOperationComplete(nameof(CreateProcessOwnerAssignmentAsync), new { Id = assignment.Id });
            return assignment.Id;
        }
        catch (Exception ex)
        {
            LogOperationError(nameof(CreateProcessOwnerAssignmentAsync), ex);
            throw;
        }
    }

    public async Task UpdateProcessOwnerAssignmentAsync(UpdateProcessOwnerAssignmentRequest request, CancellationToken cancellationToken = default)
    {
        LogOperationStart(nameof(UpdateProcessOwnerAssignmentAsync), request);

        try
        {
            var assignment = await Context.ProcessOwnerAssignments
                .FirstOrDefaultAsync(poa => poa.Id == request.Id, cancellationToken);

            if (assignment == null)
            {
                throw new NotFoundException($"Process owner assignment with ID {request.Id} not found");
            }

            await ValidateUpdateProcessOwnerAssignmentRequestAsync(request, assignment, cancellationToken);

            // Update assignment properties
            assignment.ProcessOwnerUserId = request.ProcessOwnerUserId;
            assignment.FactoryId = request.FactoryId;
            assignment.AreaId = request.AreaId;
            assignment.SubAreaId = request.SubAreaId;
            assignment.Description = request.Description;
            assignment.IsActive = request.IsActive;
            assignment.UpdatedByUserId = CurrentUserService.UserId;
            assignment.UpdatedAt = DateTime.UtcNow;

            await Context.SaveChangesAsync(cancellationToken);
            LogOperationComplete(nameof(UpdateProcessOwnerAssignmentAsync));
        }
        catch (Exception ex)
        {
            LogOperationError(nameof(UpdateProcessOwnerAssignmentAsync), ex);
            throw;
        }
    }

    public async Task DeleteProcessOwnerAssignmentAsync(int id, CancellationToken cancellationToken = default)
    {
        LogOperationStart(nameof(DeleteProcessOwnerAssignmentAsync), new { Id = id });

        try
        {
            var assignment = await Context.ProcessOwnerAssignments
                .FirstOrDefaultAsync(poa => poa.Id == id, cancellationToken);

            if (assignment == null)
            {
                throw new NotFoundException($"Process owner assignment with ID {id} not found");
            }

            Context.ProcessOwnerAssignments.Remove(assignment);
            await Context.SaveChangesAsync(cancellationToken);
            LogOperationComplete(nameof(DeleteProcessOwnerAssignmentAsync));
        }
        catch (Exception ex)
        {
            LogOperationError(nameof(DeleteProcessOwnerAssignmentAsync), ex);
            throw;
        }
    }

    // Helper methods for validation
    private async Task ValidateCreateUserGroupRequestAsync(CreateUserGroupRequest request, CancellationToken cancellationToken)
    {
        // Check if group name already exists
        var existingGroup = await Context.UserGroups
            .FirstOrDefaultAsync(ug => ug.GroupName == request.GroupName, cancellationToken);

        if (existingGroup != null)
        {
            throw new ValidationException($"User group with name '{request.GroupName}' already exists");
        }

        // Validate member IDs exist
        if (request.MemberIds.Any())
        {
            var existingUserIds = await Context.Users
                .Where(u => request.MemberIds.Contains(u.Id))
                .Select(u => u.Id)
                .ToListAsync(cancellationToken);

            var invalidIds = request.MemberIds.Except(existingUserIds).ToList();
            if (invalidIds.Any())
            {
                throw new ValidationException($"Invalid user IDs: {string.Join(", ", invalidIds)}");
            }
        }
    }

    private async Task ValidateUpdateUserGroupRequestAsync(UpdateUserGroupRequest request, UserGroup existingGroup, CancellationToken cancellationToken)
    {
        // Check if group name already exists (excluding current group)
        var existingGroupWithName = await Context.UserGroups
            .FirstOrDefaultAsync(ug => ug.GroupName == request.GroupName && ug.Id != request.Id.ToString(), cancellationToken);

        if (existingGroupWithName != null)
        {
            throw new ValidationException($"User group with name '{request.GroupName}' already exists");
        }

        // Validate member IDs exist
        if (request.MemberIds.Any())
        {
            var existingUserIds = await Context.Users
                .Where(u => request.MemberIds.Contains(u.Id))
                .Select(u => u.Id)
                .ToListAsync(cancellationToken);

            var invalidIds = request.MemberIds.Except(existingUserIds).ToList();
            if (invalidIds.Any())
            {
                throw new ValidationException($"Invalid user IDs: {string.Join(", ", invalidIds)}");
            }
        }

        // Check record version for optimistic concurrency
        if (existingGroup.RecordVersion != request.RecordVersion)
        {
            throw new ConcurrencyException("User group has been modified by another user. Please refresh and try again.");
        }
    }

    // Helper methods for AD sync
    private async Task<bool> CreateUserFromAdAsync(AdUserDto adUser, bool dryRun, CancellationToken cancellationToken)
    {
        // Determine role based on AD groups
        var role = await DetermineUserRoleFromAdGroupsAsync(adUser.MemberOf.ToList(), cancellationToken);
        if (role == null)
        {
            Logger.LogWarning("Could not determine role for AD user {Username}, skipping", adUser.Username);
            return false;
        }

        if (!dryRun)
        {
            var userId = CuidGenerator.Generate();
            var user = User.Create(
                id: userId,
                username: adUser.Username,
                firstName: adUser.FirstName,
                lastName: adUser.LastName,
                email: adUser.Email,
                roleId: role.Id,
                factoryId: null, // Will be assigned separately if needed
                isActive: adUser.IsEnabled,
                adObjectGuid: adUser.ObjectGuid,
                adDistinguishedName: adUser.DistinguishedName,
                createdByUserId: "SYSTEM_AD_SYNC");

            user.AdSyncLastDate = DateTime.UtcNow;
            await Context.Users.AddAsync(user, cancellationToken);
        }

        Logger.LogInformation("Created user {Username} with role {Role} {DryRun}",
            adUser.Username, role.RoleName, dryRun ? "(DRY RUN)" : "");
        return true;
    }

    private async Task<bool> UpdateUserFromAdAsync(User dbUser, AdUserDto adUser, bool dryRun, CancellationToken cancellationToken)
    {
        var hasChanges = false;

        // Check for changes
        if (dbUser.FirstName != adUser.FirstName ||
            dbUser.LastName != adUser.LastName ||
            dbUser.Email != adUser.Email ||
            dbUser.AdDistinguishedName != adUser.DistinguishedName ||
            dbUser.IsActive != adUser.IsEnabled)
        {
            hasChanges = true;
        }

        if (hasChanges && !dryRun)
        {
            dbUser.FirstName = adUser.FirstName;
            dbUser.LastName = adUser.LastName;
            dbUser.Email = adUser.Email;
            dbUser.AdDistinguishedName = adUser.DistinguishedName;
            dbUser.IsActive = adUser.IsEnabled;
            dbUser.AdSyncLastDate = DateTime.UtcNow;
            dbUser.UpdatedByUserId = "SYSTEM_AD_SYNC";
            dbUser.UpdatedAt = DateTime.UtcNow;

            Logger.LogDebug("Updated user {Username} from AD {DryRun}",
                dbUser.Username, dryRun ? "(DRY RUN)" : "");
        }

        return hasChanges;
    }

    private async Task<Role?> DetermineUserRoleFromAdGroupsAsync(List<string> adGroups, CancellationToken cancellationToken)
    {
        // Get AD group role mappings
        var mappings = await Context.AdGroupRoleMappings
            .Include(m => m.Role)
            .Where(m => m.IsActive && adGroups.Contains(m.AdGroupName))
            .OrderBy(m => m.Role.RoleName) // Order by role priority if needed
            .ToListAsync(cancellationToken);

        // Return the first matching role (could implement priority logic here)
        return mappings.FirstOrDefault()?.Role;
    }

    // Additional validation methods
    private async Task ValidateCreateAdGroupRoleMappingRequestAsync(CreateAdGroupRoleMappingRequest request, CancellationToken cancellationToken)
    {
        // Check if mapping already exists
        var existingMapping = await Context.AdGroupRoleMappings
            .FirstOrDefaultAsync(m => m.AdGroupName == request.AdGroupName, cancellationToken);

        if (existingMapping != null)
        {
            throw new ValidationException($"AD group role mapping for '{request.AdGroupName}' already exists");
        }

        // Validate role exists
        var roleExists = await Context.Roles
            .AnyAsync(r => r.Id == request.RoleId, cancellationToken);

        if (!roleExists)
        {
            throw new ValidationException($"Role with ID {request.RoleId} not found");
        }
    }

    private async Task ValidateUpdateAdGroupRoleMappingRequestAsync(UpdateAdGroupRoleMappingRequest request, AdGroupRoleMapping existingMapping, CancellationToken cancellationToken)
    {
        // Check if group name already exists (excluding current mapping)
        var existingMappingWithName = await Context.AdGroupRoleMappings
            .FirstOrDefaultAsync(m => m.AdGroupName == request.AdGroupName && m.Id != request.Id, cancellationToken);

        if (existingMappingWithName != null)
        {
            throw new ValidationException($"AD group role mapping for '{request.AdGroupName}' already exists");
        }

        // Validate role exists
        var roleExists = await Context.Roles
            .AnyAsync(r => r.Id == request.RoleId, cancellationToken);

        if (!roleExists)
        {
            throw new ValidationException($"Role with ID {request.RoleId} not found");
        }

        // Check record version for optimistic concurrency
        if (existingMapping.RecordVersion != request.RecordVersion)
        {
            throw new ConcurrencyException("AD group role mapping has been modified by another user. Please refresh and try again.");
        }
    }

    private async Task ValidateCreateProcessOwnerAssignmentRequestAsync(CreateProcessOwnerAssignmentRequest request, CancellationToken cancellationToken)
    {
        // Validate user exists and has ProcessOwner role
        var user = await Context.Users
            .Include(u => u.Role)
            .FirstOrDefaultAsync(u => u.Id == request.ProcessOwnerUserId, cancellationToken);

        if (user == null)
        {
            throw new ValidationException($"User with ID {request.ProcessOwnerUserId} not found");
        }

        if (user.Role.RoleName != UserRole.ProcessOwner)
        {
            throw new ValidationException($"User {user.Username} does not have ProcessOwner role");
        }

        // Validate organizational hierarchy
        if (request.FactoryId.HasValue)
        {
            var factoryExists = await Context.Factories
                .AnyAsync(f => f.Id == request.FactoryId.Value, cancellationToken);

            if (!factoryExists)
            {
                throw new ValidationException($"Factory with ID {request.FactoryId} not found");
            }
        }

        if (request.AreaId.HasValue)
        {
            var areaExists = await Context.Areas
                .AnyAsync(a => a.Id == request.AreaId.Value, cancellationToken);

            if (!areaExists)
            {
                throw new ValidationException($"Area with ID {request.AreaId} not found");
            }
        }

        if (request.SubAreaId.HasValue)
        {
            var subAreaExists = await Context.SubAreas
                .AnyAsync(sa => sa.Id == request.SubAreaId.Value, cancellationToken);

            if (!subAreaExists)
            {
                throw new ValidationException($"SubArea with ID {request.SubAreaId} not found");
            }
        }

        // Check for duplicate assignment
        var existingAssignment = await Context.ProcessOwnerAssignments
            .AnyAsync(poa => poa.ProcessOwnerUserId == request.ProcessOwnerUserId &&
                           poa.FactoryId == request.FactoryId &&
                           poa.AreaId == request.AreaId &&
                           poa.SubAreaId == request.SubAreaId &&
                           poa.IsActive, cancellationToken);

        if (existingAssignment)
        {
            throw new ValidationException("A process owner assignment already exists for this scope");
        }
    }

    private async Task ValidateUpdateProcessOwnerAssignmentRequestAsync(UpdateProcessOwnerAssignmentRequest request, ProcessOwnerAssignment existingAssignment, CancellationToken cancellationToken)
    {
        // Validate user exists and has ProcessOwner role
        var user = await Context.Users
            .Include(u => u.Role)
            .FirstOrDefaultAsync(u => u.Id == request.ProcessOwnerUserId, cancellationToken);

        if (user == null)
        {
            throw new ValidationException($"User with ID {request.ProcessOwnerUserId} not found");
        }

        if (user.Role.RoleName != UserRole.ProcessOwner)
        {
            throw new ValidationException($"User {user.Username} does not have ProcessOwner role");
        }

        // Validate organizational hierarchy (same as create)
        if (request.FactoryId.HasValue)
        {
            var factoryExists = await Context.Factories
                .AnyAsync(f => f.Id == request.FactoryId.Value, cancellationToken);

            if (!factoryExists)
            {
                throw new ValidationException($"Factory with ID {request.FactoryId} not found");
            }
        }

        // Check record version for optimistic concurrency
        if (existingAssignment.RecordVersion != request.RecordVersion)
        {
            throw new ConcurrencyException("Process owner assignment has been modified by another user. Please refresh and try again.");
        }
    }

    private static string GetScopeDescription(ProcessOwnerAssignment assignment)
    {
        var parts = new List<string>();

        if (assignment.Factory != null)
            parts.Add($"Factory: {assignment.Factory.FactoryName}");

        if (assignment.Area != null)
            parts.Add($"Area: {assignment.Area.AreaName}");

        if (assignment.SubArea != null)
            parts.Add($"SubArea: {assignment.SubArea.SubAreaName}");

        return parts.Any() ? string.Join(" > ", parts) : "All Locations";
    }
}
