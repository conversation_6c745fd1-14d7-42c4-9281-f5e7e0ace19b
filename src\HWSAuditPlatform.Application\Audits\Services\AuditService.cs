using HWSAuditPlatform.Application.Audits.DTOs;
using HWSAuditPlatform.Application.Audits.Extensions;
using HWSAuditPlatform.Application.Audits.Services;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Exceptions;
using HWSAuditPlatform.Application.Interfaces;
using HWSAuditPlatform.Domain.Entities.Audits;
using HWSAuditPlatform.Domain.Enums;
using HWSAuditPlatform.Domain.Services;
using Cuid.Net;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace HWSAuditPlatform.Application.Audits.Services;

/// <summary>
/// Service for audit operations
/// </summary>
public class AuditService : BaseService<AuditService>, IAuditService
{
    private readonly ITemplateAccessService _templateAccessService;
    private readonly IAuditLogService _auditLogService;

    public AuditService(
        IApplicationDbContext context,
        ICurrentUserService currentUserService,
        ILogger<AuditService> logger,
        ITemplateAccessService templateAccessService,
        IAuditLogService auditLogService) 
        : base(context, currentUserService, logger)
    {
        _templateAccessService = templateAccessService;
        _auditLogService = auditLogService;
    }

    public async Task<string> CreateAuditAsync(CreateAuditRequest request, CancellationToken cancellationToken = default)
    {
        LogOperationStart(nameof(CreateAuditAsync), request);

        try
        {
            await ValidateCreateAuditRequestAsync(request, cancellationToken);

            // Create the audit entity
            var audit = new Audit
            {
                Id = Cuid.Generate(),
                AuditTemplateId = request.AuditTemplateId,
                AssignmentType = request.AssignmentType,
                AssignedToUserGroupId = request.AssignedToUserGroupId,
                AssignedToUserId = request.AssignedToUserId,
                ScheduledDate = request.ScheduledDate,
                DueDate = request.DueDate,
                OverallStatus = AuditOverallStatus.Scheduled,
                FactoryId = request.FactoryId,
                AreaId = request.AreaId,
                SubAreaId = request.SubAreaId,
                CreatedByUserId = CurrentUserService.UserId,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            await Context.Audits.AddAsync(audit, cancellationToken);
            await Context.SaveChangesAsync(cancellationToken);

            // Log the business operation
            var assignmentDetails = request.AssignmentType == AssignmentType.Individual
                ? $"assigned to user {request.AssignedToUserId}"
                : $"assigned to group {request.AssignedToUserGroupId}";

            await _auditLogService.LogBusinessOperationAsync(
                "AuditCreated",
                "Audit",
                audit.Id,
                $"Created audit using template {request.AuditTemplateId}, {assignmentDetails}, scheduled for {request.ScheduledDate:yyyy-MM-dd}",
                cancellationToken);

            LogOperationComplete(nameof(CreateAuditAsync), new { Id = audit.Id });
            return audit.Id;
        }
        catch (Exception ex)
        {
            LogOperationError(nameof(CreateAuditAsync), ex);
            throw;
        }
    }

    public async Task<AuditDto> GetAuditAsync(GetAuditRequest request, CancellationToken cancellationToken = default)
    {
        LogOperationStart(nameof(GetAuditAsync), request);

        try
        {
            var audit = await Context.Audits
                .Include(a => a.AuditTemplate)
                    .ThenInclude(t => t.QuestionGroups.Where(g => g.IsActive))
                    .ThenInclude(g => g.Questions.Where(q => q.IsActive))
                    .ThenInclude(q => q.Options.Where(o => o.IsActive))
                .Include(a => a.AuditTemplate)
                    .ThenInclude(t => t.Questions.Where(q => q.IsActive))
                    .ThenInclude(q => q.Options.Where(o => o.IsActive))
                .Include(a => a.AuditTemplate)
                    .ThenInclude(t => t.Questions.Where(q => q.IsActive))
                    .ThenInclude(q => q.AllowedEvidenceTypes.Where(aet => aet.IsActive))
                .Include(a => a.AssignedToUser)
                .Include(a => a.AssignedToUserGroup)
                .Include(a => a.Factory)
                .Include(a => a.Area)
                .Include(a => a.SubArea)
                .Include(a => a.ReviewedByUser)
                .FirstOrDefaultAsync(a => a.Id == request.AuditId, cancellationToken);

            if (audit == null)
            {
                throw new NotFoundException(nameof(Audit), request.AuditId);
            }

            var result = audit.ToDto();
            LogOperationComplete(nameof(GetAuditAsync), new { Id = request.AuditId });
            return result;
        }
        catch (Exception ex)
        {
            LogOperationError(nameof(GetAuditAsync), ex);
            throw;
        }
    }

    public async Task<PaginatedResult<AuditSummaryDto>> GetAuditsAsync(GetAuditsRequest request, CancellationToken cancellationToken = default)
    {
        LogOperationStart(nameof(GetAuditsAsync), request);

        try
        {
            var query = Context.Audits
                .Include(a => a.AuditTemplate)
                .Include(a => a.AssignedToUser)
                .Include(a => a.Factory)
                .Include(a => a.Area)
                .AsQueryable();

            // Apply filters
            if (!string.IsNullOrEmpty(request.SearchTerm))
            {
                var searchPattern = $"%{request.SearchTerm}%";
                query = query.Where(a =>
                    EF.Functions.Like(a.AuditTemplate.TemplateName, searchPattern) ||
                    (a.AssignedToUser != null && EF.Functions.Like(a.AssignedToUser.FirstName + " " + a.AssignedToUser.LastName, searchPattern)) ||
                    EF.Functions.Like(a.Factory.FactoryName, searchPattern) ||
                    EF.Functions.Like(a.Area.AreaName, searchPattern));
            }

            if (request.Status.HasValue)
            {
                query = query.Where(a => a.OverallStatus == request.Status.Value);
            }

            if (!string.IsNullOrEmpty(request.AssignedToUserId))
            {
                query = query.Where(a => a.AssignedToUserId == request.AssignedToUserId);
            }

            if (request.FactoryId.HasValue)
            {
                query = query.Where(a => a.FactoryId == request.FactoryId.Value);
            }

            if (request.AreaId.HasValue)
            {
                query = query.Where(a => a.AreaId == request.AreaId.Value);
            }

            if (request.AuditTemplateId.HasValue)
            {
                query = query.Where(a => a.AuditTemplateId == request.AuditTemplateId.Value);
            }

            if (request.ScheduledDateFrom.HasValue)
            {
                query = query.Where(a => a.ScheduledDate >= request.ScheduledDateFrom.Value);
            }

            if (request.ScheduledDateTo.HasValue)
            {
                query = query.Where(a => a.ScheduledDate <= request.ScheduledDateTo.Value);
            }

            if (request.DueDateFrom.HasValue)
            {
                query = query.Where(a => a.DueDate >= request.DueDateFrom.Value);
            }

            if (request.DueDateTo.HasValue)
            {
                query = query.Where(a => a.DueDate <= request.DueDateTo.Value);
            }

            if (request.IsOverdue.HasValue)
            {
                var now = DateTime.UtcNow;
                if (request.IsOverdue.Value)
                {
                    query = query.Where(a => a.DueDate.HasValue && a.DueDate.Value < now &&
                        a.OverallStatus != AuditOverallStatus.Closed &&
                        a.OverallStatus != AuditOverallStatus.Cancelled);
                }
                else
                {
                    query = query.Where(a => !a.DueDate.HasValue || a.DueDate.Value >= now ||
                        a.OverallStatus == AuditOverallStatus.Closed ||
                        a.OverallStatus == AuditOverallStatus.Cancelled);
                }
            }

            // Apply sorting
            query = ApplySorting(query, request.SortBy, request.SortDirection);

            // Get total count
            var totalCount = await query.CountAsync(cancellationToken);

            // Apply pagination
            var audits = await query
                .Skip((request.PageNumber - 1) * request.PageSize)
                .Take(request.PageSize)
                .Select(a => new AuditSummaryDto
                {
                    Id = a.Id,
                    AuditTemplateName = a.AuditTemplate.TemplateName,
                    AssignedToUserName = a.AssignedToUser != null ? $"{a.AssignedToUser.FirstName} {a.AssignedToUser.LastName}".Trim() : null,
                    ScheduledDate = a.ScheduledDate,
                    DueDate = a.DueDate,
                    OverallStatus = a.OverallStatus,
                    FactoryName = a.Factory.FactoryName,
                    AreaName = a.Area.AreaName,
                    OverallScore = a.OverallScore,
                    IsOverdue = a.DueDate.HasValue && a.DueDate.Value < DateTime.UtcNow && 
                        a.OverallStatus != AuditOverallStatus.Closed && 
                        a.OverallStatus != AuditOverallStatus.Cancelled
                })
                .ToListAsync(cancellationToken);

            var result = new PaginatedResult<AuditSummaryDto>
            {
                Items = audits,
                TotalCount = totalCount,
                PageNumber = request.PageNumber,
                PageSize = request.PageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / request.PageSize)
            };

            LogOperationComplete(nameof(GetAuditsAsync), new { Count = audits.Count, TotalCount = totalCount });
            return result;
        }
        catch (Exception ex)
        {
            LogOperationError(nameof(GetAuditsAsync), ex);
            throw;
        }
    }

    public async Task<PaginatedResult<AuditSummaryDto>> GetMyAuditsAsync(GetMyAuditsRequest request, CancellationToken cancellationToken = default)
    {
        LogOperationStart(nameof(GetMyAuditsAsync), request);

        try
        {
            var currentUserId = CurrentUserService.UserId;
            if (string.IsNullOrEmpty(currentUserId))
            {
                throw new UnauthorizedAccessException("User not authenticated");
            }

            var query = Context.Audits
                .Include(a => a.AuditTemplate)
                .Include(a => a.AssignedToUser)
                .Include(a => a.Factory)
                .Include(a => a.Area)
                .Where(a => a.AssignedToUserId == currentUserId)
                .AsQueryable();

            // Apply filters
            if (request.Status.HasValue)
            {
                query = query.Where(a => a.OverallStatus == request.Status.Value);
            }

            if (request.FactoryId.HasValue)
            {
                query = query.Where(a => a.FactoryId == request.FactoryId.Value);
            }

            if (request.AreaId.HasValue)
            {
                query = query.Where(a => a.AreaId == request.AreaId.Value);
            }

            if (request.ScheduledDateFrom.HasValue)
            {
                query = query.Where(a => a.ScheduledDate >= request.ScheduledDateFrom.Value);
            }

            if (request.ScheduledDateTo.HasValue)
            {
                query = query.Where(a => a.ScheduledDate <= request.ScheduledDateTo.Value);
            }

            if (request.DueDateFrom.HasValue)
            {
                query = query.Where(a => a.DueDate >= request.DueDateFrom.Value);
            }

            if (request.DueDateTo.HasValue)
            {
                query = query.Where(a => a.DueDate <= request.DueDateTo.Value);
            }

            if (request.IsOverdue.HasValue)
            {
                var now = DateTime.UtcNow;
                if (request.IsOverdue.Value)
                {
                    query = query.Where(a => a.DueDate.HasValue && a.DueDate.Value < now &&
                        a.OverallStatus != AuditOverallStatus.Closed &&
                        a.OverallStatus != AuditOverallStatus.Cancelled);
                }
                else
                {
                    query = query.Where(a => !a.DueDate.HasValue || a.DueDate.Value >= now ||
                        a.OverallStatus == AuditOverallStatus.Closed ||
                        a.OverallStatus == AuditOverallStatus.Cancelled);
                }
            }

            // Apply sorting
            query = ApplySorting(query, request.SortBy, request.SortDirection);

            // Get total count
            var totalCount = await query.CountAsync(cancellationToken);

            // Apply pagination and select data
            var audits = await query
                .Skip((request.PageNumber - 1) * request.PageSize)
                .Take(request.PageSize)
                .Select(a => new AuditSummaryDto
                {
                    Id = a.Id,
                    AuditTemplateName = a.AuditTemplate.TemplateName,
                    AssignedToUserName = a.AssignedToUser != null ? $"{a.AssignedToUser.FirstName} {a.AssignedToUser.LastName}".Trim() : null,
                    ScheduledDate = a.ScheduledDate,
                    DueDate = a.DueDate,
                    OverallStatus = a.OverallStatus,
                    FactoryName = a.Factory.FactoryName,
                    AreaName = a.Area.AreaName,
                    OverallScore = a.OverallScore,
                    IsOverdue = a.DueDate.HasValue && a.DueDate.Value < DateTime.UtcNow &&
                        a.OverallStatus != AuditOverallStatus.Closed &&
                        a.OverallStatus != AuditOverallStatus.Cancelled
                })
                .ToListAsync(cancellationToken);

            var result = new PaginatedResult<AuditSummaryDto>
            {
                Items = audits,
                TotalCount = totalCount,
                PageNumber = request.PageNumber,
                PageSize = request.PageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / request.PageSize)
            };

            LogOperationComplete(nameof(GetMyAuditsAsync), new { Count = audits.Count, TotalCount = totalCount });
            return result;
        }
        catch (Exception ex)
        {
            LogOperationError(nameof(GetMyAuditsAsync), ex);
            throw;
        }
    }

    public async Task<PaginatedResult<AuditSummaryDto>> GetMyAuditHistoryAsync(GetMyAuditHistoryRequest request, CancellationToken cancellationToken = default)
    {
        LogOperationStart(nameof(GetMyAuditHistoryAsync), request);

        try
        {
            var currentUserId = CurrentUserService.UserId;
            if (string.IsNullOrEmpty(currentUserId))
            {
                throw new UnauthorizedAccessException("User not authenticated");
            }

            var query = Context.Audits
                .Include(a => a.AuditTemplate)
                .Include(a => a.AssignedToUser)
                .Include(a => a.Factory)
                .Include(a => a.Area)
                .Where(a => a.AssignedToUserId == currentUserId &&
                           (a.OverallStatus == AuditOverallStatus.Submitted ||
                            a.OverallStatus == AuditOverallStatus.ManagerReviewed ||
                            a.OverallStatus == AuditOverallStatus.PendingManagerReview ||
                            a.OverallStatus == AuditOverallStatus.Closed ||
                            a.OverallStatus == AuditOverallStatus.PendingCorrection))
                .AsQueryable();

            // Apply filters
            if (request.FactoryId.HasValue)
            {
                query = query.Where(a => a.FactoryId == request.FactoryId.Value);
            }

            if (request.AreaId.HasValue)
            {
                query = query.Where(a => a.AreaId == request.AreaId.Value);
            }

            if (request.CompletedDateFrom.HasValue)
            {
                query = query.Where(a => a.CompletedAt >= request.CompletedDateFrom.Value);
            }

            if (request.CompletedDateTo.HasValue)
            {
                query = query.Where(a => a.CompletedAt <= request.CompletedDateTo.Value);
            }

            // Apply sorting
            query = ApplySorting(query, request.SortBy, request.SortDirection);

            // Get total count
            var totalCount = await query.CountAsync(cancellationToken);

            // Apply pagination and select data
            var audits = await query
                .Skip((request.PageNumber - 1) * request.PageSize)
                .Take(request.PageSize)
                .Select(a => new AuditSummaryDto
                {
                    Id = a.Id,
                    AuditTemplateName = a.AuditTemplate.TemplateName,
                    AssignedToUserName = a.AssignedToUser != null ? $"{a.AssignedToUser.FirstName} {a.AssignedToUser.LastName}".Trim() : null,
                    ScheduledDate = a.ScheduledDate,
                    DueDate = a.DueDate,
                    OverallStatus = a.OverallStatus,
                    FactoryName = a.Factory.FactoryName,
                    AreaName = a.Area.AreaName,
                    OverallScore = a.OverallScore,
                    IsOverdue = a.DueDate.HasValue && a.DueDate.Value < DateTime.UtcNow &&
                        a.OverallStatus != AuditOverallStatus.Closed &&
                        a.OverallStatus != AuditOverallStatus.Cancelled
                })
                .ToListAsync(cancellationToken);

            var result = new PaginatedResult<AuditSummaryDto>
            {
                Items = audits,
                TotalCount = totalCount,
                PageNumber = request.PageNumber,
                PageSize = request.PageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / request.PageSize)
            };

            LogOperationComplete(nameof(GetMyAuditHistoryAsync), new { Count = audits.Count, TotalCount = totalCount });
            return result;
        }
        catch (Exception ex)
        {
            LogOperationError(nameof(GetMyAuditHistoryAsync), ex);
            throw;
        }
    }

    public async Task<PaginatedResult<AuditReviewDto>> GetCompletedAuditsForReviewAsync(GetCompletedAuditsForReviewRequest request, CancellationToken cancellationToken = default)
    {
        LogOperationStart(nameof(GetCompletedAuditsForReviewAsync), request);

        try
        {
            var query = Context.Audits
                .Include(a => a.AuditTemplate)
                .Include(a => a.AssignedToUser)
                .Include(a => a.Factory)
                .Include(a => a.Area)
                .Include(a => a.SubArea)
                .Include(a => a.ReviewedByUser)
                .Where(a => a.OverallStatus == AuditOverallStatus.PendingManagerReview ||
                           a.OverallStatus == AuditOverallStatus.ManagerReviewed)
                .AsQueryable();

            // Apply filters
            if (request.FactoryId.HasValue)
            {
                query = query.Where(a => a.FactoryId == request.FactoryId.Value);
            }

            if (request.AreaId.HasValue)
            {
                query = query.Where(a => a.AreaId == request.AreaId.Value);
            }

            if (request.CompletedDateFrom.HasValue)
            {
                query = query.Where(a => a.CompletedAt >= request.CompletedDateFrom.Value);
            }

            if (request.CompletedDateTo.HasValue)
            {
                query = query.Where(a => a.CompletedAt <= request.CompletedDateTo.Value);
            }

            // Apply sorting
            query = ApplySorting(query, request.SortBy, request.SortDirection);

            // Get total count
            var totalCount = await query.CountAsync(cancellationToken);

            // Apply pagination and select data
            var audits = await query
                .Skip((request.PageNumber - 1) * request.PageSize)
                .Take(request.PageSize)
                .ToListAsync(cancellationToken);

            var auditDtos = new List<AuditForReviewDto>();
            foreach (var audit in audits)
            {
                var dto = audit.ToForReviewDto();

                // Get audit answers and calculate statistics
                var answers = await Context.AuditAnswers
                    .Include(aa => aa.Question)
                    .Include(aa => aa.SelectedOption)
                    .Include(aa => aa.SelectedOptions)
                        .ThenInclude(so => so.QuestionOption)
                    .Include(aa => aa.FailureReasons)
                    .Include(aa => aa.Attachments)
                    .Where(aa => aa.AuditId == audit.Id)
                    .ToListAsync(cancellationToken);

                dto.Answers = answers.ToDtos();

                // Calculate statistics
                var totalQuestions = await Context.Questions
                    .Where(q => q.AuditTemplateId == audit.AuditTemplateId && q.IsActive)
                    .CountAsync(cancellationToken);

                dto.TotalQuestions = totalQuestions;
                dto.AnsweredQuestions = answers.Count;
                dto.PassedQuestions = answers.Count(a => a.AnswerValue == "Yes" || a.AnswerValue == "Pass");
                dto.FailedQuestions = answers.Count(a => a.AnswerValue == "No" || a.AnswerValue == "Fail");
                dto.NotApplicableQuestions = answers.Count(a => a.IsNotApplicable);
                dto.QuestionsWithFindings = answers.Count(a => a.FailureReasons.Any());
                dto.TotalAttachments = answers.Sum(a => a.Attachments.Count);

                auditDtos.Add(dto);
            }

            var result = new PaginatedResult<AuditForReviewDto>
            {
                Items = auditDtos,
                TotalCount = totalCount,
                PageNumber = request.PageNumber,
                PageSize = request.PageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / request.PageSize)
            };

            LogOperationComplete(nameof(GetCompletedAuditsForReviewAsync), new { Count = auditDtos.Count, TotalCount = totalCount });
            return result;
        }
        catch (Exception ex)
        {
            LogOperationError(nameof(GetCompletedAuditsForReviewAsync), ex);
            throw;
        }
    }

    public async Task<AuditReviewDto> GetAuditForReviewAsync(GetAuditForReviewRequest request, CancellationToken cancellationToken = default)
    {
        LogOperationStart(nameof(GetAuditForReviewAsync), request);

        try
        {
            var audit = await Context.Audits
                .Include(a => a.AuditTemplate)
                .Include(a => a.AssignedToUser)
                .Include(a => a.Factory)
                .Include(a => a.Area)
                .Include(a => a.SubArea)
                .Include(a => a.ReviewedByUser)
                .FirstOrDefaultAsync(a => a.Id == request.AuditId, cancellationToken);

            if (audit == null)
            {
                throw new NotFoundException(nameof(Audit), request.AuditId);
            }

            var dto = audit.ToForReviewDto();

            // Get audit answers
            var answers = await Context.AuditAnswers
                .Include(aa => aa.Question)
                .Include(aa => aa.SelectedOption)
                .Include(aa => aa.SelectedOptions)
                    .ThenInclude(so => so.QuestionOption)
                .Include(aa => aa.FailureReasons)
                .Include(aa => aa.Attachments)
                .Where(aa => aa.AuditId == audit.Id)
                .ToListAsync(cancellationToken);

            dto.Answers = answers.ToDtos();

            // Calculate statistics
            var totalQuestions = await Context.Questions
                .Where(q => q.AuditTemplateId == audit.AuditTemplateId && q.IsActive)
                .CountAsync(cancellationToken);

            dto.TotalQuestions = totalQuestions;
            dto.AnsweredQuestions = answers.Count;
            dto.PassedQuestions = answers.Count(a => a.AnswerValue == "Yes" || a.AnswerValue == "Pass");
            dto.FailedQuestions = answers.Count(a => a.AnswerValue == "No" || a.AnswerValue == "Fail");
            dto.NotApplicableQuestions = answers.Count(a => a.IsNotApplicable);
            dto.QuestionsWithFindings = answers.Count(a => a.FailureReasons.Any());
            dto.TotalAttachments = answers.Sum(a => a.Attachments.Count);

            LogOperationComplete(nameof(GetAuditForReviewAsync), new { Id = request.AuditId });
            return dto;
        }
        catch (Exception ex)
        {
            LogOperationError(nameof(GetAuditForReviewAsync), ex);
            throw;
        }
    }

    public async Task StartAuditAsync(StartAuditRequest request, CancellationToken cancellationToken = default)
    {
        LogOperationStart(nameof(StartAuditAsync), request);

        try
        {
            var audit = await Context.Audits
                .Include(a => a.AssignedToUserGroup)
                .FirstOrDefaultAsync(a => a.Id == request.AuditId, cancellationToken);

            if (audit == null)
            {
                throw new NotFoundException(nameof(Audit), request.AuditId);
            }

            // Validate audit can be started
            if (audit.OverallStatus != AuditOverallStatus.Scheduled)
            {
                throw new ValidationException($"Audit cannot be started. Current status: {audit.OverallStatus}");
            }

            // Update audit status
            audit.OverallStatus = AuditOverallStatus.InProgress;
            audit.StartedAt = DateTime.UtcNow;
            audit.UpdatedAt = DateTime.UtcNow;
            audit.UpdatedByUserId = CurrentUserService.UserId;

            // If assigned to a group with "any" assignment, assign to current user
            if (audit.AssignmentType == AssignmentType.GroupAny)
            {
                audit.AssignedToUserId = CurrentUserService.UserId;
                audit.AssignmentType = AssignmentType.Individual;
            }

            await Context.SaveChangesAsync(cancellationToken);

            await _auditLogService.LogBusinessOperationAsync(
                "AuditStarted",
                "Audit",
                audit.Id,
                $"Audit started by user {CurrentUserService.UserId}",
                cancellationToken);

            LogOperationComplete(nameof(StartAuditAsync), new { Id = request.AuditId });
        }
        catch (Exception ex)
        {
            LogOperationError(nameof(StartAuditAsync), ex);
            throw;
        }
    }

    public async Task SubmitAuditAsync(SubmitAuditRequest request, CancellationToken cancellationToken = default)
    {
        LogOperationStart(nameof(SubmitAuditAsync), request);

        try
        {
            var audit = await Context.Audits
                .Include(a => a.AuditTemplate)
                .FirstOrDefaultAsync(a => a.Id == request.AuditId, cancellationToken);

            if (audit == null)
            {
                throw new NotFoundException(nameof(Audit), request.AuditId);
            }

            // Validate audit can be submitted
            if (audit.OverallStatus != AuditOverallStatus.InProgress)
            {
                throw new ValidationException($"Audit cannot be submitted. Current status: {audit.OverallStatus}");
            }

            // Calculate overall score
            var answers = await Context.AuditAnswers
                .Where(aa => aa.AuditId == audit.Id)
                .ToListAsync(cancellationToken);

            var totalQuestions = await Context.Questions
                .Where(q => q.AuditTemplateId == audit.AuditTemplateId && q.IsActive)
                .CountAsync(cancellationToken);

            if (totalQuestions > 0)
            {
                var passedQuestions = answers.Count(a => a.AnswerValue == "Yes" || a.AnswerValue == "Pass");
                var applicableQuestions = totalQuestions - answers.Count(a => a.IsNotApplicable);

                if (applicableQuestions > 0)
                {
                    audit.OverallScore = Math.Round((decimal)passedQuestions / applicableQuestions * 100, 2);
                }
            }

            // Update audit status
            audit.OverallStatus = AuditOverallStatus.PendingManagerReview;
            audit.CompletedAt = DateTime.UtcNow;
            audit.UpdatedAt = DateTime.UtcNow;
            audit.UpdatedByUserId = CurrentUserService.UserId;

            if (!string.IsNullOrEmpty(request.AuditorComments))
            {
                audit.ManagerComments = request.AuditorComments; // Note: This might need a separate field for auditor comments
            }

            await Context.SaveChangesAsync(cancellationToken);

            await _auditLogService.LogBusinessOperationAsync(
                "AuditSubmitted",
                "Audit",
                audit.Id,
                $"Audit submitted by user {CurrentUserService.UserId} with score {audit.OverallScore}%",
                cancellationToken);

            LogOperationComplete(nameof(SubmitAuditAsync), new { Id = request.AuditId, Score = audit.OverallScore });
        }
        catch (Exception ex)
        {
            LogOperationError(nameof(SubmitAuditAsync), ex);
            throw;
        }
    }

    public async Task ReviewAuditAsync(ReviewAuditRequest request, CancellationToken cancellationToken = default)
    {
        LogOperationStart(nameof(ReviewAuditAsync), request);

        try
        {
            var audit = await Context.Audits
                .FirstOrDefaultAsync(a => a.Id == request.AuditId, cancellationToken);

            if (audit == null)
            {
                throw new NotFoundException(nameof(Audit), request.AuditId);
            }

            // Validate audit can be reviewed
            if (audit.OverallStatus != AuditOverallStatus.PendingManagerReview)
            {
                throw new ValidationException($"Audit cannot be reviewed. Current status: {audit.OverallStatus}");
            }

            // Update audit based on review decision
            if (request.Approved)
            {
                audit.OverallStatus = AuditOverallStatus.ManagerReviewed;
            }
            else
            {
                audit.OverallStatus = AuditOverallStatus.PendingCorrection;
            }

            audit.ReviewedByUserId = CurrentUserService.UserId;
            audit.ReviewedAt = DateTime.UtcNow;
            audit.ManagerComments = request.Comments;
            audit.UpdatedAt = DateTime.UtcNow;
            audit.UpdatedByUserId = CurrentUserService.UserId;

            await Context.SaveChangesAsync(cancellationToken);

            var reviewAction = request.Approved ? "approved" : "rejected";
            await _auditLogService.LogBusinessOperationAsync(
                "AuditReviewed",
                "Audit",
                audit.Id,
                $"Audit {reviewAction} by manager {CurrentUserService.UserId}",
                cancellationToken);

            LogOperationComplete(nameof(ReviewAuditAsync), new { Id = request.AuditId, Approved = request.Approved });
        }
        catch (Exception ex)
        {
            LogOperationError(nameof(ReviewAuditAsync), ex);
            throw;
        }
    }

    public async Task<string> SubmitAuditAnswerAsync(SubmitAuditAnswerRequest request, CancellationToken cancellationToken = default)
    {
        LogOperationStart(nameof(SubmitAuditAnswerAsync), request);

        try
        {
            // Validate audit exists and is in progress
            var audit = await Context.Audits
                .FirstOrDefaultAsync(a => a.Id == request.AuditId, cancellationToken);

            if (audit == null)
            {
                throw new NotFoundException(nameof(Audit), request.AuditId);
            }

            if (audit.OverallStatus != AuditOverallStatus.InProgress)
            {
                throw new ValidationException($"Cannot submit answer for audit with status: {audit.OverallStatus}");
            }

            // Validate question exists
            var question = await Context.Questions
                .FirstOrDefaultAsync(q => q.Id == request.QuestionId, cancellationToken);

            if (question == null)
            {
                throw new NotFoundException(nameof(Question), request.QuestionId);
            }

            // Check if answer already exists
            var existingAnswer = await Context.AuditAnswers
                .FirstOrDefaultAsync(aa => aa.AuditId == request.AuditId && aa.QuestionId == request.QuestionId, cancellationToken);

            AuditAnswer auditAnswer;
            if (existingAnswer != null)
            {
                // Update existing answer
                auditAnswer = existingAnswer;
                SetAnswerValue(auditAnswer, request.AnswerValue, question.QuestionType);
                auditAnswer.SelectedOptionId = request.SelectedOptionIds.FirstOrDefault();
                auditAnswer.IsNotApplicable = request.IsNotApplicable;
                auditAnswer.Comments = request.Comments;
                auditAnswer.SeverityLevel = request.SeverityLevel;
                auditAnswer.UpdatedAt = DateTime.UtcNow;
                auditAnswer.UpdatedByUserId = CurrentUserService.UserId;

                // Clear existing selected options and failure reasons
                var existingSelectedOptions = await Context.AuditAnswerSelectedOptions
                    .Where(aso => aso.AuditAnswerId == auditAnswer.Id)
                    .ToListAsync(cancellationToken);
                Context.AuditAnswerSelectedOptions.RemoveRange(existingSelectedOptions);

                var existingFailureReasons = await Context.AuditAnswerFailureReasons
                    .Where(afr => afr.AuditAnswerId == auditAnswer.Id)
                    .ToListAsync(cancellationToken);
                Context.AuditAnswerFailureReasons.RemoveRange(existingFailureReasons);
            }
            else
            {
                // Create new answer
                auditAnswer = new AuditAnswer
                {
                    Id = Cuid.Generate(),
                    AuditId = request.AuditId,
                    QuestionId = request.QuestionId,
                    SelectedOptionId = request.SelectedOptionIds.FirstOrDefault(),
                    IsNotApplicable = request.IsNotApplicable,
                    Comments = request.Comments,
                    SeverityLevel = request.SeverityLevel,
                    CreatedAt = DateTime.UtcNow,
                    CreatedByUserId = CurrentUserService.UserId,
                    UpdatedAt = DateTime.UtcNow,
                    UpdatedByUserId = CurrentUserService.UserId
                };

                SetAnswerValue(auditAnswer, request.AnswerValue, question.QuestionType);
                await Context.AuditAnswers.AddAsync(auditAnswer, cancellationToken);
            }

            // Add selected options
            foreach (var optionId in request.SelectedOptionIds)
            {
                var selectedOption = new AuditAnswerSelectedOption
                {
                    Id = Cuid.Generate(),
                    AuditAnswerId = auditAnswer.Id,
                    QuestionOptionId = optionId
                };
                await Context.AuditAnswerSelectedOptions.AddAsync(selectedOption, cancellationToken);
            }

            // Add failure reasons
            foreach (var reason in request.FailureReasons)
            {
                var failureReason = new AuditAnswerFailureReason
                {
                    Id = Cuid.Generate(),
                    AuditAnswerId = auditAnswer.Id,
                    ReasonText = reason
                };
                await Context.AuditAnswerFailureReasons.AddAsync(failureReason, cancellationToken);
            }

            await Context.SaveChangesAsync(cancellationToken);

            // TODO: Create finding if needed (this would require IFindingService)
            // For now, we'll skip the finding creation that was in the original MediatR handler

            await _auditLogService.LogBusinessOperationAsync(
                "AuditAnswerSubmitted",
                "AuditAnswer",
                auditAnswer.Id,
                $"Answer submitted for question {request.QuestionId} in audit {request.AuditId}",
                cancellationToken);

            LogOperationComplete(nameof(SubmitAuditAnswerAsync), new { Id = auditAnswer.Id });
            return auditAnswer.Id;
        }
        catch (Exception ex)
        {
            LogOperationError(nameof(SubmitAuditAnswerAsync), ex);
            throw;
        }
    }

    public async Task DeleteAuditAnswerAsync(DeleteAuditAnswerRequest request, CancellationToken cancellationToken = default)
    {
        LogOperationStart(nameof(DeleteAuditAnswerAsync), request);

        try
        {
            var auditAnswer = await Context.AuditAnswers
                .Include(aa => aa.Audit)
                .FirstOrDefaultAsync(aa => aa.Id == request.AuditAnswerId, cancellationToken);

            if (auditAnswer == null)
            {
                throw new NotFoundException(nameof(AuditAnswer), request.AuditAnswerId);
            }

            // Validate audit is in progress
            if (auditAnswer.Audit.OverallStatus != AuditOverallStatus.InProgress)
            {
                throw new ValidationException($"Cannot delete answer for audit with status: {auditAnswer.Audit.OverallStatus}");
            }

            // Remove related entities
            var selectedOptions = await Context.AuditAnswerSelectedOptions
                .Where(aso => aso.AuditAnswerId == auditAnswer.Id)
                .ToListAsync(cancellationToken);
            Context.AuditAnswerSelectedOptions.RemoveRange(selectedOptions);

            var failureReasons = await Context.AuditAnswerFailureReasons
                .Where(afr => afr.AuditAnswerId == auditAnswer.Id)
                .ToListAsync(cancellationToken);
            Context.AuditAnswerFailureReasons.RemoveRange(failureReasons);

            var attachments = await Context.AuditAttachments
                .Where(aa => aa.AuditAnswerId == auditAnswer.Id)
                .ToListAsync(cancellationToken);
            Context.AuditAttachments.RemoveRange(attachments);

            // Remove the answer itself
            Context.AuditAnswers.Remove(auditAnswer);

            await Context.SaveChangesAsync(cancellationToken);

            await _auditLogService.LogBusinessOperationAsync(
                "AuditAnswerDeleted",
                "AuditAnswer",
                auditAnswer.Id,
                $"Answer deleted for question {auditAnswer.QuestionId} in audit {auditAnswer.AuditId}",
                cancellationToken);

            LogOperationComplete(nameof(DeleteAuditAnswerAsync), new { Id = request.AuditAnswerId });
        }
        catch (Exception ex)
        {
            LogOperationError(nameof(DeleteAuditAnswerAsync), ex);
            throw;
        }
    }

    public async Task<List<AuditAnswerDto>> GetAuditAnswersAsync(GetAuditAnswersRequest request, CancellationToken cancellationToken = default)
    {
        LogOperationStart(nameof(GetAuditAnswersAsync), request);

        try
        {
            // Verify audit exists
            var auditExists = await Context.Audits
                .AnyAsync(a => a.Id == request.AuditId, cancellationToken);

            if (!auditExists)
            {
                throw new NotFoundException(nameof(Audit), request.AuditId);
            }

            var query = Context.AuditAnswers
                .Include(a => a.Question)
                .Include(a => a.SelectedOption)
                .Include(a => a.SelectedOptions)
                    .ThenInclude(so => so.QuestionOption)
                .Include(a => a.FailureReasons)
                .Include(a => a.Attachments)
                .Where(a => a.AuditId == request.AuditId);

            // Apply question filter if specified
            if (request.QuestionId.HasValue)
            {
                query = query.Where(a => a.QuestionId == request.QuestionId.Value);
            }

            var answers = await query
                .OrderBy(a => a.Question.DisplayOrder)
                .ToListAsync(cancellationToken);

            var result = answers.ToDtos();
            LogOperationComplete(nameof(GetAuditAnswersAsync), new { Count = result.Count, AuditId = request.AuditId });
            return result;
        }
        catch (Exception ex)
        {
            LogOperationError(nameof(GetAuditAnswersAsync), ex);
            throw;
        }
    }

    public async Task<AuditAnswerDto> GetAuditAnswerAsync(GetAuditAnswerRequest request, CancellationToken cancellationToken = default)
    {
        LogOperationStart(nameof(GetAuditAnswerAsync), request);

        try
        {
            var auditAnswer = await Context.AuditAnswers
                .Include(a => a.Question)
                .Include(a => a.SelectedOption)
                .Include(a => a.SelectedOptions)
                    .ThenInclude(so => so.QuestionOption)
                .Include(a => a.FailureReasons)
                .Include(a => a.Attachments)
                .FirstOrDefaultAsync(a => a.Id == request.AuditAnswerId, cancellationToken);

            if (auditAnswer == null)
            {
                throw new NotFoundException(nameof(AuditAnswer), request.AuditAnswerId);
            }

            var result = auditAnswer.ToDto();
            LogOperationComplete(nameof(GetAuditAnswerAsync), new { Id = request.AuditAnswerId });
            return result;
        }
        catch (Exception ex)
        {
            LogOperationError(nameof(GetAuditAnswerAsync), ex);
            throw;
        }
    }

    #region Private Helper Methods

    private async Task ValidateCreateAuditRequestAsync(CreateAuditRequest request, CancellationToken cancellationToken)
    {
        // Validate template exists and is published
        var template = await Context.AuditTemplates
            .FirstOrDefaultAsync(t => t.Id == request.AuditTemplateId, cancellationToken);

        if (template == null)
        {
            throw new NotFoundException(nameof(AuditTemplate), request.AuditTemplateId);
        }

        if (!template.IsPublished)
        {
            throw new ValidationException("Cannot create audit from unpublished template");
        }

        // Validate template access
        var hasAccess = await _templateAccessService.HasTemplateAccessAsync(
            CurrentUserService.UserId,
            request.AuditTemplateId,
            cancellationToken);

        if (!hasAccess)
        {
            throw new ValidationException("User does not have access to this audit template");
        }

        // Validate factory exists
        var factoryExists = await Context.Factories
            .AnyAsync(f => f.Id == request.FactoryId, cancellationToken);

        if (!factoryExists)
        {
            throw new NotFoundException(nameof(Factory), request.FactoryId);
        }

        // Validate area exists and belongs to factory
        var area = await Context.Areas
            .FirstOrDefaultAsync(a => a.Id == request.AreaId, cancellationToken);

        if (area == null)
        {
            throw new NotFoundException(nameof(Area), request.AreaId);
        }

        if (area.FactoryId != request.FactoryId)
        {
            throw new ValidationException("Area does not belong to the specified factory");
        }

        // Validate sub-area if specified
        if (request.SubAreaId.HasValue)
        {
            var subArea = await Context.SubAreas
                .FirstOrDefaultAsync(sa => sa.Id == request.SubAreaId.Value, cancellationToken);

            if (subArea == null)
            {
                throw new NotFoundException(nameof(SubArea), request.SubAreaId.Value);
            }

            if (subArea.AreaId != request.AreaId)
            {
                throw new ValidationException("Sub-area does not belong to the specified area");
            }
        }

        // Validate assignment
        if (request.AssignmentType == AssignmentType.Individual)
        {
            if (string.IsNullOrEmpty(request.AssignedToUserId))
            {
                throw new ValidationException("AssignedToUserId is required for individual assignment");
            }

            var userExists = await Context.Users
                .AnyAsync(u => u.Id == request.AssignedToUserId, cancellationToken);

            if (!userExists)
            {
                throw new NotFoundException(nameof(User), request.AssignedToUserId);
            }
        }
        else if (request.AssignmentType == AssignmentType.GroupAll || request.AssignmentType == AssignmentType.GroupAny)
        {
            if (string.IsNullOrEmpty(request.AssignedToUserGroupId))
            {
                throw new ValidationException("AssignedToUserGroupId is required for group assignment");
            }

            var groupExists = await Context.UserGroups
                .AnyAsync(g => g.Id == request.AssignedToUserGroupId, cancellationToken);

            if (!groupExists)
            {
                throw new NotFoundException(nameof(UserGroup), request.AssignedToUserGroupId);
            }
        }

        // Validate dates
        if (request.DueDate.HasValue && request.DueDate.Value <= request.ScheduledDate)
        {
            throw new ValidationException("Due date must be after scheduled date");
        }
    }

    private IQueryable<Audit> ApplySorting(IQueryable<Audit> query, string? sortBy, string? sortDirection)
    {
        var isDescending = string.Equals(sortDirection, "desc", StringComparison.OrdinalIgnoreCase);

        return sortBy?.ToLower() switch
        {
            "templatename" => isDescending
                ? query.OrderByDescending(a => a.AuditTemplate.TemplateName)
                : query.OrderBy(a => a.AuditTemplate.TemplateName),
            "assigneduser" => isDescending
                ? query.OrderByDescending(a => a.AssignedToUser.FirstName + " " + a.AssignedToUser.LastName)
                : query.OrderBy(a => a.AssignedToUser.FirstName + " " + a.AssignedToUser.LastName),
            "scheduleddate" => isDescending
                ? query.OrderByDescending(a => a.ScheduledDate)
                : query.OrderBy(a => a.ScheduledDate),
            "duedate" => isDescending
                ? query.OrderByDescending(a => a.DueDate)
                : query.OrderBy(a => a.DueDate),
            "status" => isDescending
                ? query.OrderByDescending(a => a.OverallStatus)
                : query.OrderBy(a => a.OverallStatus),
            "factory" => isDescending
                ? query.OrderByDescending(a => a.Factory.FactoryName)
                : query.OrderBy(a => a.Factory.FactoryName),
            "area" => isDescending
                ? query.OrderByDescending(a => a.Area.AreaName)
                : query.OrderBy(a => a.Area.AreaName),
            "score" => isDescending
                ? query.OrderByDescending(a => a.OverallScore)
                : query.OrderBy(a => a.OverallScore),
            "completeddate" => isDescending
                ? query.OrderByDescending(a => a.CompletedAt)
                : query.OrderBy(a => a.CompletedAt),
            _ => query.OrderByDescending(a => a.CreatedAt) // Default sort
        };
    }

    private void SetAnswerValue(AuditAnswer auditAnswer, string? answerValue, QuestionType questionType)
    {
        // Clear all answer fields first
        auditAnswer.AnswerBoolean = null;
        auditAnswer.AnswerText = null;
        auditAnswer.AnswerNumeric = null;
        auditAnswer.AnswerDate = null;
        auditAnswer.OriginalAnswerValue = answerValue;

        if (string.IsNullOrEmpty(answerValue))
            return;

        switch (questionType)
        {
            case QuestionType.YesNo:
                if (bool.TryParse(answerValue, out var boolValue))
                {
                    auditAnswer.AnswerBoolean = boolValue;
                }
                break;

            case QuestionType.ShortText:
            case QuestionType.LongText:
                auditAnswer.AnswerText = answerValue;
                break;

            case QuestionType.Numeric:
                if (decimal.TryParse(answerValue, out var numericValue))
                {
                    auditAnswer.AnswerNumeric = numericValue;
                }
                break;

            case QuestionType.Date:
                if (DateTime.TryParse(answerValue, out var dateValue))
                {
                    auditAnswer.AnswerDate = dateValue;
                }
                break;

            case QuestionType.SingleSelect:
            case QuestionType.MultiSelect:
                // For select types, the actual selection is handled via SelectedOptionId and SelectedOptions
                // Store the display value in OriginalAnswerValue
                break;

            default:
                auditAnswer.AnswerText = answerValue;
                break;
        }
    }

    #endregion
}
