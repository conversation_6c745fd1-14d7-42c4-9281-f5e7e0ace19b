using Asp.Versioning;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using HWSAuditPlatform.Application.Common;
// using HWSAuditPlatform.Application.Scheduling.Commands.CreateRecurringAuditSetting;
// using HWSAuditPlatform.Application.Scheduling.Commands.UpdateRecurringAuditSetting;
// using HWSAuditPlatform.Application.Scheduling.Commands.DeleteRecurringAuditSetting;
// using HWSAuditPlatform.Application.Scheduling.Commands.ToggleRecurringAuditSetting;
// using HWSAuditPlatform.Application.Scheduling.Commands.GenerateRecurringAudits;
// using HWSAuditPlatform.Application.Scheduling.Queries.GetRecurringAuditSettings;
// using HWSAuditPlatform.Application.Scheduling.Queries.GetRecurringAuditSetting;
using HWSAuditPlatform.Application.Scheduling.DTOs;

namespace HWSAuditPlatform.ApiService.Controllers;

/// <summary>
/// Controller for managing recurring audit settings
/// </summary>
[ApiController]
[ApiVersion("1.0")]
[Route("api/v{version:apiVersion}/recurring-audits")]
[Authorize]
public class RecurringAuditsController : BaseController
{
    public RecurringAuditsController(ILogger<RecurringAuditsController> logger)
        : base(logger)
    {
    }

    /// <summary>
    /// Get a paginated list of recurring audit settings
    /// </summary>
    /// <param name="query">Query parameters for filtering and pagination</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Paginated list of recurring audit settings</returns>
    [HttpGet]
    [Authorize(Policy = "AuditorOrAbove")]
    [ProducesResponseType(typeof(PaginatedResult<RecurringAuditSettingSummaryDto>), 200)]
    public async Task<ActionResult<PaginatedResult<RecurringAuditSettingSummaryDto>>> GetRecurringAuditSettings(
        [FromQuery] GetRecurringAuditSettingsQuery query,
        CancellationToken cancellationToken)
    {
        Logger.LogInformation("Getting recurring audit settings with filters: SearchTerm={SearchTerm}, IsEnabled={IsEnabled}, PageNumber={PageNumber}, PageSize={PageSize}",
            query.SearchTerm, query.IsEnabled, query.PageNumber, query.PageSize);

        var result = await Mediator.Send(query, cancellationToken);
        return Success(result);
    }

    /// <summary>
    /// Get a specific recurring audit setting by ID
    /// </summary>
    /// <param name="id">Recurring audit setting ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Recurring audit setting details</returns>
    [HttpGet("{id}")]
    [Authorize(Policy = "AuditorOrAbove")]
    [ProducesResponseType(typeof(RecurringAuditSettingDto), 200)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<RecurringAuditSettingDto>> GetRecurringAuditSetting(
        string id,
        CancellationToken cancellationToken)
    {
        Logger.LogInformation("Getting recurring audit setting with ID: {Id}", id);

        var query = new GetRecurringAuditSettingQuery(id);
        var result = await Mediator.Send(query, cancellationToken);
        return Success(result);
    }

    /// <summary>
    /// Create a new recurring audit setting
    /// </summary>
    /// <param name="command">Recurring audit setting data</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Created recurring audit setting ID</returns>
    [HttpPost]
    [Authorize(Policy = "ProcessOwnerOrAbove")]
    [ProducesResponseType(typeof(string), 201)]
    [ProducesResponseType(400)]
    public async Task<ActionResult<string>> CreateRecurringAuditSetting(
        CreateRecurringAuditSettingCommand command,
        CancellationToken cancellationToken)
    {
        Logger.LogInformation("Creating recurring audit setting: {SettingName}", command.SettingName);

        var id = await Mediator.Send(command, cancellationToken);
        return Created(nameof(GetRecurringAuditSettings), new { }, id, "Recurring audit setting created successfully");
    }

    /// <summary>
    /// Update an existing recurring audit setting
    /// </summary>
    /// <param name="id">Recurring audit setting ID</param>
    /// <param name="command">Updated recurring audit setting data</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Success response</returns>
    [HttpPut("{id}")]
    [Authorize(Policy = "ProcessOwnerOrAbove")]
    [ProducesResponseType(204)]
    [ProducesResponseType(404)]
    [ProducesResponseType(409)] // Concurrency conflict
    public async Task<ActionResult> UpdateRecurringAuditSetting(
        string id,
        UpdateRecurringAuditSettingCommand command,
        CancellationToken cancellationToken)
    {
        Logger.LogInformation("Updating recurring audit setting with ID: {Id}", id);

        command.Id = id; // Ensure the ID from the route is used
        await Mediator.Send(command, cancellationToken);
        return NoContentSuccess("Recurring audit setting updated successfully");
    }

    /// <summary>
    /// Delete a recurring audit setting
    /// </summary>
    /// <param name="id">Recurring audit setting ID</param>
    /// <param name="recordVersion">Record version for concurrency control</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Success response</returns>
    [HttpDelete("{id}")]
    [Authorize(Policy = "ProcessOwnerOrAbove")]
    [ProducesResponseType(204)]
    [ProducesResponseType(404)]
    [ProducesResponseType(409)] // Concurrency conflict
    public async Task<ActionResult> DeleteRecurringAuditSetting(
        string id,
        [FromQuery] int recordVersion,
        CancellationToken cancellationToken)
    {
        Logger.LogInformation("Deleting recurring audit setting with ID: {Id}", id);

        var command = new DeleteRecurringAuditSettingCommand(id, recordVersion);
        await Mediator.Send(command, cancellationToken);
        return NoContentSuccess("Recurring audit setting deleted successfully");
    }

    /// <summary>
    /// Enable or disable a recurring audit setting
    /// </summary>
    /// <param name="id">Recurring audit setting ID</param>
    /// <param name="isEnabled">Whether to enable or disable the setting</param>
    /// <param name="recordVersion">Record version for concurrency control</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Success response</returns>
    [HttpPatch("{id}/toggle")]
    [Authorize(Policy = "ProcessOwnerOrAbove")]
    [ProducesResponseType(204)]
    [ProducesResponseType(404)]
    [ProducesResponseType(409)] // Concurrency conflict
    public async Task<ActionResult> ToggleRecurringAuditSetting(
        string id,
        [FromQuery] bool isEnabled,
        [FromQuery] int recordVersion,
        CancellationToken cancellationToken)
    {
        Logger.LogInformation("Toggling recurring audit setting {Id} to {IsEnabled}", id, isEnabled);

        var command = new ToggleRecurringAuditSettingCommand(id, isEnabled, recordVersion);
        await Mediator.Send(command, cancellationToken);
        
        var action = isEnabled ? "enabled" : "disabled";
        return NoContentSuccess($"Recurring audit setting {action} successfully");
    }

    /// <summary>
    /// Pause a recurring audit setting (convenience method)
    /// </summary>
    /// <param name="id">Recurring audit setting ID</param>
    /// <param name="recordVersion">Record version for concurrency control</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Success response</returns>
    [HttpPost("{id}/pause")]
    [Authorize(Policy = "ProcessOwnerOrAbove")]
    [ProducesResponseType(204)]
    [ProducesResponseType(404)]
    [ProducesResponseType(409)] // Concurrency conflict
    public async Task<ActionResult> PauseRecurringAuditSetting(
        string id,
        [FromQuery] int recordVersion,
        CancellationToken cancellationToken)
    {
        Logger.LogInformation("Pausing recurring audit setting with ID: {Id}", id);

        var command = new ToggleRecurringAuditSettingCommand(id, false, recordVersion);
        await Mediator.Send(command, cancellationToken);
        return NoContentSuccess("Recurring audit setting paused successfully");
    }

    /// <summary>
    /// Resume a recurring audit setting (convenience method)
    /// </summary>
    /// <param name="id">Recurring audit setting ID</param>
    /// <param name="recordVersion">Record version for concurrency control</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Success response</returns>
    [HttpPost("{id}/resume")]
    [Authorize(Policy = "ProcessOwnerOrAbove")]
    [ProducesResponseType(204)]
    [ProducesResponseType(404)]
    [ProducesResponseType(409)] // Concurrency conflict
    public async Task<ActionResult> ResumeRecurringAuditSetting(
        string id,
        [FromQuery] int recordVersion,
        CancellationToken cancellationToken)
    {
        Logger.LogInformation("Resuming recurring audit setting with ID: {Id}", id);

        var command = new ToggleRecurringAuditSettingCommand(id, true, recordVersion);
        await Mediator.Send(command, cancellationToken);
        return NoContentSuccess("Recurring audit setting resumed successfully");
    }

    /// <summary>
    /// Manually trigger audit generation for all due recurring audit settings or a specific setting
    /// </summary>
    /// <param name="settingId">Optional specific recurring audit setting ID to process</param>
    /// <param name="dryRun">Whether this is a dry run (don't actually create audits, just report what would be created)</param>
    /// <param name="maxAuditsToGenerate">Maximum number of audits to generate in this batch</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Generation result with statistics</returns>
    [HttpPost("generate")]
    [Authorize(Policy = "ProcessOwnerOrAbove")]
    [ProducesResponseType(typeof(GenerateRecurringAuditsResult), 200)]
    [ProducesResponseType(400)]
    public async Task<ActionResult<GenerateRecurringAuditsResult>> GenerateRecurringAudits(
        [FromQuery] string? settingId = null,
        [FromQuery] bool dryRun = false,
        [FromQuery] int maxAuditsToGenerate = 100,
        CancellationToken cancellationToken = default)
    {
        Logger.LogInformation("Manually triggering recurring audit generation - SettingId: {SettingId}, DryRun: {DryRun}, MaxAudits: {MaxAudits}",
            settingId, dryRun, maxAuditsToGenerate);

        var command = new GenerateRecurringAuditsCommand
        {
            RecurringAuditSettingId = settingId,
            DryRun = dryRun,
            MaxAuditsToGenerate = maxAuditsToGenerate
        };

        var result = await Mediator.Send(command, cancellationToken);

        var message = dryRun
            ? $"Dry run completed - would generate {result.GeneratedAuditIds.Count} audits from {result.SettingsProcessed} settings"
            : $"Generated {result.GeneratedAuditIds.Count} audits from {result.SettingsProcessed} settings";

        return Success(result, message);
    }

    /// <summary>
    /// Preview what audits would be generated for a specific recurring audit setting (dry run)
    /// </summary>
    /// <param name="id">Recurring audit setting ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Preview of audits that would be generated</returns>
    [HttpPost("{id}/preview")]
    [Authorize(Policy = "ProcessOwnerOrAbove")]
    [ProducesResponseType(typeof(GenerateRecurringAuditsResult), 200)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<GenerateRecurringAuditsResult>> PreviewRecurringAuditGeneration(
        string id,
        CancellationToken cancellationToken)
    {
        Logger.LogInformation("Previewing audit generation for recurring audit setting: {Id}", id);

        var command = new GenerateRecurringAuditsCommand
        {
            RecurringAuditSettingId = id,
            DryRun = true,
            MaxAuditsToGenerate = 100
        };

        var result = await Mediator.Send(command, cancellationToken);
        return Success(result, $"Preview completed - would generate {result.GeneratedAuditIds.Count} audits");
    }
}
