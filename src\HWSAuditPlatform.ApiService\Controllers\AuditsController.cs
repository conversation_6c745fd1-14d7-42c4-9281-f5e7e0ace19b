using Asp.Versioning;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using HWSAuditPlatform.Application.Common;
using HWSAuditPlatform.Application.Audits.Services;
using HWSAuditPlatform.Application.Audits.DTOs;
using HWSAuditPlatform.Domain.Enums;
using static HWSAuditPlatform.Application.Audits.Services.IAuditService;

namespace HWSAuditPlatform.ApiService.Controllers;

/// <summary>
/// Request model for audit review operations
/// </summary>
public class ReviewAuditApiRequest
{
    /// <summary>
    /// Whether the audit is approved or rejected
    /// </summary>
    public bool Approved { get; set; }

    /// <summary>
    /// Optional comments from the reviewer
    /// </summary>
    public string? Comments { get; set; }
}

/// <summary>
/// Request model for audit submission operations
/// </summary>
public class SubmitAuditRequest
{
    /// <summary>
    /// Optional comments from the auditor
    /// </summary>
    public string? AuditorComments { get; set; }
}

/// <summary>
/// Controller for audit management operations
/// </summary>
[Route("api/v{version:apiVersion}/[controller]")]
[ApiVersion("1.0")]
public class AuditsController : BaseController
{
    private readonly IAuditService _auditService;

    public AuditsController(IAuditService auditService, ILogger<AuditsController> logger)
        : base(logger)
    {
        _auditService = auditService;
    }

    /// <summary>
    /// Get a paginated list of audits
    /// </summary>
    /// <param name="pageNumber">Page number</param>
    /// <param name="pageSize">Page size</param>
    /// <param name="searchTerm">Search term</param>
    /// <param name="status">Audit status filter</param>
    /// <param name="factoryId">Factory ID filter</param>
    /// <param name="assignedToUserId">Assigned user ID filter</param>
    /// <param name="areaId">Area ID filter</param>
    /// <param name="templateId">Template ID filter</param>
    /// <param name="scheduledDateFrom">Scheduled date from filter</param>
    /// <param name="scheduledDateTo">Scheduled date to filter</param>
    /// <param name="dueDateFrom">Due date from filter</param>
    /// <param name="dueDateTo">Due date to filter</param>
    /// <param name="isOverdue">Overdue filter</param>
    /// <param name="sortBy">Sort field</param>
    /// <param name="sortDirection">Sort direction</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Paginated list of audits</returns>
    [HttpGet]
    [Authorize(Policy = "AuditorOrAbove")]
    [ProducesResponseType(typeof(PaginatedResult<AuditSummaryDto>), 200)]
    public async Task<ActionResult<PaginatedResult<AuditSummaryDto>>> GetAudits(
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] string? searchTerm = null,
        [FromQuery] string? status = null,
        [FromQuery] int? factoryId = null,
        [FromQuery] string? assignedToUserId = null,
        [FromQuery] int? areaId = null,
        [FromQuery] int? templateId = null,
        [FromQuery] DateTime? scheduledDateFrom = null,
        [FromQuery] DateTime? scheduledDateTo = null,
        [FromQuery] DateTime? dueDateFrom = null,
        [FromQuery] DateTime? dueDateTo = null,
        [FromQuery] bool? isOverdue = null,
        [FromQuery] string sortBy = "ScheduledDate",
        [FromQuery] string sortDirection = "desc",
        CancellationToken cancellationToken = default)
    {
        Logger.LogInformation("Getting audits - Page: {PageNumber}, Size: {PageSize}", pageNumber, pageSize);

        // For non-admin users, filter by their factory
        var currentUserFactoryId = GetCurrentUserFactoryId();
        if (!HasAnyRole("DevAdmin") && currentUserFactoryId.HasValue)
        {
            factoryId = currentUserFactoryId.Value;
        }

        // Parse status enum if provided
        AuditOverallStatus? statusEnum = null;
        if (!string.IsNullOrEmpty(status) && Enum.TryParse<AuditOverallStatus>(status, true, out var parsedStatus))
        {
            statusEnum = parsedStatus;
        }

        var request = new GetAuditsRequest
        {
            PageNumber = pageNumber,
            PageSize = Math.Min(pageSize, 50),
            SearchTerm = searchTerm,
            Status = statusEnum,
            FactoryId = factoryId,
            AssignedToUserId = assignedToUserId,
            AreaId = areaId,
            AuditTemplateId = templateId,
            ScheduledDateFrom = scheduledDateFrom,
            ScheduledDateTo = scheduledDateTo,
            DueDateFrom = dueDateFrom,
            DueDateTo = dueDateTo,
            IsOverdue = isOverdue,
            SortBy = sortBy,
            SortDirection = sortDirection
        };

        var result = await _auditService.GetAuditsAsync(request, cancellationToken);
        return Success(result);
    }

    /// <summary>
    /// Get a specific audit by ID
    /// </summary>
    /// <param name="id">Audit ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Audit details</returns>
    [HttpGet("{id}")]
    [Authorize(Policy = "AuditorOrAbove")]
    [ProducesResponseType(typeof(AuditDto), 200)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<AuditDto>> GetAudit(string id, CancellationToken cancellationToken)
    {
        Logger.LogInformation("Getting audit with ID: {AuditId}", id);

        var request = new GetAuditRequest { AuditId = id };
        var result = await _auditService.GetAuditAsync(request, cancellationToken);
        return Success(result);
    }

    /// <summary>
    /// Create a new audit
    /// </summary>
    /// <param name="command">Audit creation data</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Created audit ID</returns>
    [HttpPost]
    [Authorize(Policy = "ProcessOwnerOrAbove")]
    [ProducesResponseType(typeof(string), 201)]
    public async Task<ActionResult<string>> CreateAudit(
        CreateAuditRequest request,
        CancellationToken cancellationToken)
    {
        Logger.LogInformation("Creating audit for template: {TemplateId}", request.AuditTemplateId);
        var auditId = await _auditService.CreateAuditAsync(request, cancellationToken);
        return Created(nameof(GetAudit), new { id = auditId }, auditId, "Audit created successfully");
    }

    /// <summary>
    /// Get audits assigned to the current user
    /// </summary>
    /// <param name="pageNumber">Page number</param>
    /// <param name="pageSize">Page size</param>
    /// <param name="status">Status filter</param>
    /// <param name="factoryId">Factory ID filter</param>
    /// <param name="areaId">Area ID filter</param>
    /// <param name="isOverdue">Overdue filter</param>
    /// <param name="sortBy">Sort field</param>
    /// <param name="sortDirection">Sort direction</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Paginated list of assigned audits</returns>
    [HttpGet("my-audits")]
    [Authorize(Policy = "AuditorOrAbove")]
    [ProducesResponseType(typeof(PaginatedResult<AuditSummaryDto>), 200)]
    public async Task<ActionResult<PaginatedResult<AuditSummaryDto>>> GetMyAudits(
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] string? status = null,
        [FromQuery] int? factoryId = null,
        [FromQuery] int? areaId = null,
        [FromQuery] bool? isOverdue = null,
        [FromQuery] string sortBy = "ScheduledDate",
        [FromQuery] string sortDirection = "desc",
        CancellationToken cancellationToken = default)
    {
        var currentUserId = GetCurrentUserId();
        if (string.IsNullOrEmpty(currentUserId))
        {
            return Unauthorized();
        }

        Logger.LogInformation("Getting audits for user: {UserId}", currentUserId);

        // Parse status enum if provided
        AuditOverallStatus? statusEnum = null;
        if (!string.IsNullOrEmpty(status) && Enum.TryParse<AuditOverallStatus>(status, true, out var parsedStatus))
        {
            statusEnum = parsedStatus;
        }

        var request = new GetMyAuditsRequest
        {
            PageNumber = pageNumber,
            PageSize = Math.Min(pageSize, 50),
            Status = statusEnum,
            FactoryId = factoryId,
            AreaId = areaId,
            IsOverdue = isOverdue,
            SortBy = sortBy,
            SortDirection = sortDirection
        };

        var result = await _auditService.GetMyAuditsAsync(request, cancellationToken);
        return Success(result);
    }

    /// <summary>
    /// Start an audit
    /// </summary>
    /// <param name="id">Audit ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Success response</returns>
    [HttpPost("{id}/start")]
    [Authorize(Policy = "AuditorOrAbove")]
    [ProducesResponseType(204)]
    [ProducesResponseType(400)]
    [ProducesResponseType(404)]
    public async Task<ActionResult> StartAudit(string id, CancellationToken cancellationToken)
    {
        Logger.LogInformation("Starting audit: {AuditId}", id);

        var request = new StartAuditRequest { AuditId = id };
        await _auditService.StartAuditAsync(request, cancellationToken);
        return NoContentSuccess("Audit started successfully");
    }

    /// <summary>
    /// Submit an audit for review
    /// </summary>
    /// <param name="id">Audit ID</param>
    /// <param name="request">Submit audit request with optional comments</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Success response</returns>
    [HttpPost("{id}/submit")]
    [Authorize(Policy = "AuditorOrAbove")]
    [ProducesResponseType(204)]
    [ProducesResponseType(400)]
    [ProducesResponseType(404)]
    public async Task<ActionResult> SubmitAudit(
        string id,
        [FromBody] SubmitAuditRequest? request = null,
        CancellationToken cancellationToken = default)
    {
        Logger.LogInformation("Submitting audit: {AuditId}", id);

        var submitRequest = new SubmitAuditRequest
        {
            AuditId = id,
            AuditorComments = request?.AuditorComments
        };
        await _auditService.SubmitAuditAsync(submitRequest, cancellationToken);
        return NoContentSuccess("Audit submitted successfully");
    }

    /// <summary>
    /// Review and approve/reject an audit
    /// </summary>
    /// <param name="id">Audit ID</param>
    /// <param name="request">Review request containing approval status and comments</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Success response</returns>
    [HttpPost("{id}/review")]
    [Authorize(Policy = "ProcessOwnerOrAbove")]
    [ProducesResponseType(204)]
    [ProducesResponseType(400)]
    [ProducesResponseType(404)]
    public async Task<ActionResult> ReviewAudit(
        string id,
        [FromBody] ReviewAuditApiRequest request,
        CancellationToken cancellationToken = default)
    {
        Logger.LogInformation("Reviewing audit: {AuditId}, Approved: {Approved}", id, request.Approved);

        var reviewRequest = new ReviewAuditRequest
        {
            AuditId = id,
            Approved = request.Approved,
            Comments = request.Comments
        };
        await _auditService.ReviewAuditAsync(reviewRequest, cancellationToken);
        return NoContentSuccess($"Audit {(request.Approved ? "approved" : "rejected")} successfully");
    }

    // ===== AUDIT ANSWER ENDPOINTS =====

    /// <summary>
    /// Submit or update an audit answer
    /// </summary>
    /// <param name="id">Audit ID</param>
    /// <param name="command">Submit audit answer command</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Answer ID</returns>
    [HttpPost("{id}/answers")]
    [Authorize(Policy = "AuditorOrAbove")]
    [ProducesResponseType(typeof(string), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<string>> SubmitAuditAnswer(
        string id,
        [FromBody] SubmitAuditAnswerCommand command,
        CancellationToken cancellationToken)
    {
        Logger.LogInformation("Submitting answer for audit: {AuditId}, Question: {QuestionId}", id, command.QuestionId);

        // Ensure the audit ID in the route matches the command
        command.AuditId = id;

        var answerId = await Mediator.Send(command, cancellationToken);
        return Success(answerId, "Answer submitted successfully");
    }

    /// <summary>
    /// Get all answers for a specific audit
    /// </summary>
    /// <param name="id">Audit ID</param>
    /// <param name="questionId">Optional question ID filter</param>
    /// <param name="includeUnanswered">Whether to include unanswered questions</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of audit answers</returns>
    [HttpGet("{id}/answers")]
    [Authorize(Policy = "AuditorOrAbove")]
    [ProducesResponseType(typeof(List<AuditAnswerDto>), 200)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<List<AuditAnswerDto>>> GetAuditAnswers(
        string id,
        [FromQuery] int? questionId = null,
        [FromQuery] bool includeUnanswered = false,
        CancellationToken cancellationToken = default)
    {
        Logger.LogInformation("Getting answers for audit: {AuditId}", id);

        var query = new GetAuditAnswersQuery(id)
        {
            QuestionId = questionId,
            IncludeUnanswered = includeUnanswered
        };

        var result = await Mediator.Send(query, cancellationToken);
        return Success(result);
    }

    /// <summary>
    /// Get a specific audit answer by question ID
    /// </summary>
    /// <param name="id">Audit ID</param>
    /// <param name="questionId">Question ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Audit answer or null if not found</returns>
    [HttpGet("{id}/answers/{questionId:int}")]
    [Authorize(Policy = "AuditorOrAbove")]
    [ProducesResponseType(typeof(AuditAnswerDto), 200)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<AuditAnswerDto>> GetAuditAnswer(
        string id,
        int questionId,
        CancellationToken cancellationToken)
    {
        Logger.LogInformation("Getting answer for audit: {AuditId}, Question: {QuestionId}", id, questionId);

        var query = new GetAuditAnswerQuery(id, questionId);
        var result = await Mediator.Send(query, cancellationToken);

        if (result == null)
        {
            return NotFound($"No answer found for audit {id} and question {questionId}");
        }

        return Success(result);
    }

    /// <summary>
    /// Delete an audit answer
    /// </summary>
    /// <param name="id">Audit ID</param>
    /// <param name="answerId">Answer ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Success response</returns>
    [HttpDelete("{id}/answers/{answerId}")]
    [Authorize(Policy = "AuditorOrAbove")]
    [ProducesResponseType(204)]
    [ProducesResponseType(400)]
    [ProducesResponseType(404)]
    public async Task<ActionResult> DeleteAuditAnswer(
        string id,
        string answerId,
        CancellationToken cancellationToken)
    {
        Logger.LogInformation("Deleting answer: {AnswerId} for audit: {AuditId}", answerId, id);

        var command = new DeleteAuditAnswerCommand(answerId, id);
        await Mediator.Send(command, cancellationToken);
        return NoContentSuccess("Answer deleted successfully");
    }

    // ===== MANAGER REVIEW ENDPOINTS =====

    /// <summary>
    /// Get completed audits for manager review with filtering and pagination
    /// </summary>
    /// <param name="pageNumber">Page number (1-based)</param>
    /// <param name="pageSize">Number of items per page (max 50)</param>
    /// <param name="searchTerm">Search term for filtering</param>
    /// <param name="status">Filter by audit status</param>
    /// <param name="factoryId">Filter by factory ID</param>
    /// <param name="areaId">Filter by area ID</param>
    /// <param name="subAreaId">Filter by sub-area ID</param>
    /// <param name="templateId">Filter by audit template ID</param>
    /// <param name="completedDateFrom">Filter by completion date from</param>
    /// <param name="completedDateTo">Filter by completion date to</param>
    /// <param name="assignedToUserId">Filter by assigned user ID</param>
    /// <param name="hasFindings">Show only audits with findings</param>
    /// <param name="hasAttachments">Show only audits with attachments</param>
    /// <param name="sortBy">Sort field</param>
    /// <param name="sortDirection">Sort direction (asc/desc)</param>
    /// <param name="includeAnswers">Include detailed answers in response</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Paginated list of audit reviews</returns>
    [HttpGet("completed-for-review")]
    [Authorize(Policy = "ProcessOwnerOrAbove")]
    [ProducesResponseType(typeof(PaginatedResult<AuditReviewDto>), 200)]
    [ProducesResponseType(400)]
    public async Task<ActionResult<PaginatedResult<AuditReviewDto>>> GetCompletedAuditsForReview(
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 20,
        [FromQuery] string? searchTerm = null,
        [FromQuery] string? status = null,
        [FromQuery] int? factoryId = null,
        [FromQuery] int? areaId = null,
        [FromQuery] int? subAreaId = null,
        [FromQuery] int? templateId = null,
        [FromQuery] DateTime? completedDateFrom = null,
        [FromQuery] DateTime? completedDateTo = null,
        [FromQuery] string? assignedToUserId = null,
        [FromQuery] bool? hasFindings = null,
        [FromQuery] bool? hasAttachments = null,
        [FromQuery] string sortBy = "CompletedAt",
        [FromQuery] string sortDirection = "desc",
        [FromQuery] bool includeAnswers = false,
        CancellationToken cancellationToken = default)
    {
        Logger.LogInformation("Getting completed audits for review - Page: {PageNumber}, Size: {PageSize}", pageNumber, pageSize);

        // Parse status enum if provided
        AuditOverallStatus? statusEnum = null;
        if (!string.IsNullOrEmpty(status) && Enum.TryParse<AuditOverallStatus>(status, true, out var parsedStatus))
        {
            statusEnum = parsedStatus;
        }

        var query = new GetCompletedAuditsForReviewQuery
        {
            PageNumber = pageNumber,
            PageSize = Math.Min(pageSize, 50),
            SearchTerm = searchTerm,
            Status = statusEnum,
            FactoryId = factoryId,
            AreaId = areaId,
            SubAreaId = subAreaId,
            AuditTemplateId = templateId,
            CompletedDateFrom = completedDateFrom,
            CompletedDateTo = completedDateTo,
            AssignedToUserId = assignedToUserId,
            HasFindings = hasFindings,
            HasAttachments = hasAttachments,
            SortBy = sortBy,
            SortDirection = sortDirection,
            IncludeAnswers = includeAnswers
        };

        var result = await Mediator.Send(query, cancellationToken);
        return Success(result);
    }

    /// <summary>
    /// Get a single audit with detailed review information
    /// </summary>
    /// <param name="id">Audit ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Detailed audit review information</returns>
    [HttpGet("{id}/review")]
    [Authorize(Policy = "ProcessOwnerOrAbove")]
    [ProducesResponseType(typeof(AuditReviewDto), 200)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<AuditReviewDto>> GetAuditForReview(
        string id,
        CancellationToken cancellationToken = default)
    {
        Logger.LogInformation("Getting audit for review: {AuditId}", id);

        var query = new GetAuditForReviewQuery(id);
        var result = await Mediator.Send(query, cancellationToken);
        return Success(result);
    }

    /// <summary>
    /// Get audit history for the current user (completed audits)
    /// </summary>
    /// <param name="pageNumber">Page number (1-based)</param>
    /// <param name="pageSize">Number of items per page (max 50)</param>
    /// <param name="searchTerm">Search term for filtering</param>
    /// <param name="status">Filter by audit status</param>
    /// <param name="factoryId">Filter by factory ID</param>
    /// <param name="areaId">Filter by area ID</param>
    /// <param name="completedDateFrom">Filter by completion date from</param>
    /// <param name="completedDateTo">Filter by completion date to</param>
    /// <param name="sortBy">Sort field</param>
    /// <param name="sortDirection">Sort direction (asc/desc)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Paginated list of completed audits</returns>
    [HttpGet("my-history")]
    [Authorize(Policy = "AuditorOrAbove")]
    [ProducesResponseType(typeof(PaginatedResult<AuditSummaryDto>), 200)]
    [ProducesResponseType(400)]
    public async Task<ActionResult<PaginatedResult<AuditSummaryDto>>> GetMyAuditHistory(
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 20,
        [FromQuery] string? searchTerm = null,
        [FromQuery] string? status = null,
        [FromQuery] int? factoryId = null,
        [FromQuery] int? areaId = null,
        [FromQuery] DateTime? completedDateFrom = null,
        [FromQuery] DateTime? completedDateTo = null,
        [FromQuery] string sortBy = "CompletedAt",
        [FromQuery] string sortDirection = "desc",
        CancellationToken cancellationToken = default)
    {
        Logger.LogInformation("Getting audit history for current user - Page: {PageNumber}, Size: {PageSize}", pageNumber, pageSize);

        // Parse status enum if provided
        AuditOverallStatus? statusEnum = null;
        if (!string.IsNullOrEmpty(status) && Enum.TryParse<AuditOverallStatus>(status, true, out var parsedStatus))
        {
            statusEnum = parsedStatus;
        }

        var query = new GetMyAuditHistoryQuery
        {
            PageNumber = pageNumber,
            PageSize = Math.Min(pageSize, 50),
            SearchTerm = searchTerm,
            Status = statusEnum,
            FactoryId = factoryId,
            AreaId = areaId,
            CompletedDateFrom = completedDateFrom,
            CompletedDateTo = completedDateTo,
            SortBy = sortBy,
            SortDirection = sortDirection
        };

        var result = await Mediator.Send(query, cancellationToken);
        return Success(result);
    }
}
