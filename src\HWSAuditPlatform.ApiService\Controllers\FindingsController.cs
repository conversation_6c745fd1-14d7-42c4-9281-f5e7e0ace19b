using Asp.Versioning;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using HWSAuditPlatform.Application.Common;
// using HWSAuditPlatform.Application.Findings.Commands.CreateFinding;
// using HWSAuditPlatform.Application.Findings.Commands.CreateFindingFromAuditAnswer;
// using HWSAuditPlatform.Application.Findings.Commands.UpdateFindingStatus;
// using HWSAuditPlatform.Application.Findings.Queries.GetFinding;
// using HWSAuditPlatform.Application.Findings.Queries.GetFindings;
using HWSAuditPlatform.Application.Findings.DTOs;
using HWSAuditPlatform.ApiService.Models;
using HWSAuditPlatform.Domain.Enums;

namespace HWSAuditPlatform.ApiService.Controllers;

/// <summary>
/// Controller for managing findings
/// </summary>
[ApiVersion("1.0")]
[Route("api/v{version:apiVersion}/findings")]
[Tags("Findings")]
[Authorize]
public class FindingsController : BaseController
{
    public FindingsController(ILogger<FindingsController> logger)
        : base(logger)
    {
    }

    /// <summary>
    /// Gets findings with filtering and pagination
    /// </summary>
    /// <param name="auditId">Filter by audit ID</param>
    /// <param name="auditTemplateId">Filter by audit template ID</param>
    /// <param name="factoryId">Filter by factory ID</param>
    /// <param name="areaId">Filter by area ID</param>
    /// <param name="status">Filter by finding status</param>
    /// <param name="severityLevel">Filter by severity level</param>
    /// <param name="findingCategoryId">Filter by finding category ID</param>
    /// <param name="responsibleUserId">Filter by responsible user ID</param>
    /// <param name="reportedByUserId">Filter by reported by user ID</param>
    /// <param name="dueDateFrom">Filter by due date range - from</param>
    /// <param name="dueDateTo">Filter by due date range - to</param>
    /// <param name="createdFrom">Filter by creation date range - from</param>
    /// <param name="createdTo">Filter by creation date range - to</param>
    /// <param name="isOverdue">Show only overdue findings</param>
    /// <param name="isOpen">Show only open findings</param>
    /// <param name="searchTerm">Search term for finding description</param>
    /// <param name="pageNumber">Page number (1-based)</param>
    /// <param name="pageSize">Page size</param>
    /// <param name="sortBy">Sort field</param>
    /// <param name="sortDirection">Sort direction (asc/desc)</param>
    /// <returns>Paginated list of findings</returns>
    [HttpGet]
    [ProducesResponseType(typeof(ApiResponse<PagedResult<FindingSummaryDto>>), 200)]
    public async Task<ActionResult<PagedResult<FindingSummaryDto>>> GetFindings(
        [FromQuery] string? auditId = null,
        [FromQuery] int? auditTemplateId = null,
        [FromQuery] int? factoryId = null,
        [FromQuery] int? areaId = null,
        [FromQuery] FindingStatus? status = null,
        [FromQuery] SeverityLevel? severityLevel = null,
        [FromQuery] int? findingCategoryId = null,
        [FromQuery] string? responsibleUserId = null,
        [FromQuery] string? reportedByUserId = null,
        [FromQuery] DateOnly? dueDateFrom = null,
        [FromQuery] DateOnly? dueDateTo = null,
        [FromQuery] DateTime? createdFrom = null,
        [FromQuery] DateTime? createdTo = null,
        [FromQuery] bool? isOverdue = null,
        [FromQuery] bool? isOpen = null,
        [FromQuery] string? searchTerm = null,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 20,
        [FromQuery] string sortBy = "CreatedAt",
        [FromQuery] string sortDirection = "desc")
    {
        var query = new GetFindingsQuery
        {
            AuditId = auditId,
            AuditTemplateId = auditTemplateId,
            FactoryId = factoryId,
            AreaId = areaId,
            Status = status,
            SeverityLevel = severityLevel,
            FindingCategoryId = findingCategoryId,
            ResponsibleUserId = responsibleUserId,
            ReportedByUserId = reportedByUserId,
            DueDateFrom = dueDateFrom,
            DueDateTo = dueDateTo,
            CreatedFrom = createdFrom,
            CreatedTo = createdTo,
            IsOverdue = isOverdue,
            IsOpen = isOpen,
            SearchTerm = searchTerm,
            PageNumber = pageNumber,
            PageSize = pageSize,
            SortBy = sortBy,
            SortDirection = sortDirection
        };

        var result = await Mediator.Send(query);
        return Success(result, "Findings retrieved successfully");
    }

    /// <summary>
    /// Gets a specific finding by ID
    /// </summary>
    /// <param name="id">Finding ID</param>
    /// <param name="includeCorrectiveActions">Whether to include corrective actions</param>
    /// <returns>Finding details</returns>
    [HttpGet("{id}")]
    [ProducesResponseType(typeof(ApiResponse<FindingDto>), 200)]
    [ProducesResponseType(typeof(ApiErrorResponse), 404)]
    public async Task<ActionResult<FindingDto>> GetFinding(
        string id, 
        [FromQuery] bool includeCorrectiveActions = true)
    {
        var query = new GetFindingQuery
        {
            FindingId = id,
            IncludeCorrectiveActions = includeCorrectiveActions
        };

        var result = await Mediator.Send(query);
        return Success(result, "Finding retrieved successfully");
    }

    /// <summary>
    /// Creates a new finding from an audit answer
    /// </summary>
    /// <param name="command">Finding creation data</param>
    /// <returns>Created finding</returns>
    [HttpPost]
    [Authorize(Policy = "AuditorOrAbove")]
    [ProducesResponseType(typeof(ApiResponse<FindingDto>), 201)]
    [ProducesResponseType(typeof(ApiErrorResponse), 400)]
    [ProducesResponseType(typeof(ApiErrorResponse), 404)]
    public async Task<ActionResult<FindingDto>> CreateFinding(
        [FromBody] CreateFindingCommand command)
    {
        var result = await Mediator.Send(command);
        return Created(nameof(GetFinding), new { id = result.Id }, result, 
            "Finding created successfully");
    }

    /// <summary>
    /// Updates the status of a finding
    /// </summary>
    /// <param name="id">Finding ID</param>
    /// <param name="request">Status update request</param>
    /// <returns>Success confirmation</returns>
    [HttpPut("{id}/status")]
    [Authorize(Policy = "ProcessOwnerOrAbove")]
    [ProducesResponseType(typeof(ApiResponse<object>), 200)]
    [ProducesResponseType(typeof(ApiErrorResponse), 400)]
    [ProducesResponseType(typeof(ApiErrorResponse), 404)]
    public async Task<ActionResult> UpdateFindingStatus(
        string id, 
        [FromBody] UpdateFindingStatusRequest request)
    {
        var command = new UpdateFindingStatusCommand
        {
            FindingId = id,
            Status = request.Status,
            StatusChangeNotes = request.StatusChangeNotes
        };

        var success = await Mediator.Send(command);

        if (!success)
        {
            return BadRequest(ApiErrorResponse.BadRequest("Failed to update finding status."));
        }

        return Success("Finding status updated successfully");
    }

    /// <summary>
    /// Gets findings for a specific audit
    /// </summary>
    /// <param name="auditId">Audit ID</param>
    /// <returns>List of findings for the audit</returns>
    [HttpGet("audit/{auditId}")]
    [ProducesResponseType(typeof(ApiResponse<List<FindingSummaryDto>>), 200)]
    public async Task<ActionResult<List<FindingSummaryDto>>> GetAuditFindings(string auditId)
    {
        var query = new GetFindingsQuery
        {
            AuditId = auditId,
            PageSize = int.MaxValue // Get all findings for the audit
        };

        var result = await Mediator.Send(query);
        return Success(result.Items.ToList(), "Audit findings retrieved successfully");
    }

    /// <summary>
    /// Gets overdue findings
    /// </summary>
    /// <param name="factoryId">Optional factory filter</param>
    /// <param name="areaId">Optional area filter</param>
    /// <param name="pageNumber">Page number</param>
    /// <param name="pageSize">Page size</param>
    /// <returns>Paginated list of overdue findings</returns>
    [HttpGet("overdue")]
    [ProducesResponseType(typeof(ApiResponse<PagedResult<FindingSummaryDto>>), 200)]
    public async Task<ActionResult<PagedResult<FindingSummaryDto>>> GetOverdueFindings(
        [FromQuery] int? factoryId = null,
        [FromQuery] int? areaId = null,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 20)
    {
        var query = new GetFindingsQuery
        {
            FactoryId = factoryId,
            AreaId = areaId,
            IsOverdue = true,
            PageNumber = pageNumber,
            PageSize = pageSize,
            SortBy = "DueDate",
            SortDirection = "asc"
        };

        var result = await Mediator.Send(query);
        return Success(result, "Overdue findings retrieved successfully");
    }

    /// <summary>
    /// Creates a finding from an audit answer that indicates a failure
    /// </summary>
    /// <param name="auditAnswerId">Audit answer ID</param>
    /// <param name="forceCreation">Whether to force creation even if no failure indicators are present</param>
    /// <returns>Created finding or null if no finding was needed</returns>
    [HttpPost("from-audit-answer/{auditAnswerId}")]
    [Authorize(Policy = "ProcessOwnerOrAbove")]
    [ProducesResponseType(typeof(ApiResponse<FindingDto>), 201)]
    [ProducesResponseType(typeof(ApiResponse<object>), 200)]
    [ProducesResponseType(typeof(ApiErrorResponse), 400)]
    [ProducesResponseType(typeof(ApiErrorResponse), 404)]
    public async Task<ActionResult<FindingDto>> CreateFindingFromAuditAnswer(
        string auditAnswerId,
        [FromQuery] bool forceCreation = false)
    {
        var command = new CreateFindingFromAuditAnswerCommand
        {
            AuditAnswerId = auditAnswerId,
            ForceCreation = forceCreation
        };

        var result = await Mediator.Send(command);

        if (result == null)
        {
            return Success("No finding was created as the audit answer does not indicate a failure");
        }

        return Created(nameof(GetFinding), new { id = result.Id }, result,
            "Finding created successfully from audit answer");
    }
}

/// <summary>
/// Request model for updating finding status
/// </summary>
public class UpdateFindingStatusRequest
{
    /// <summary>
    /// The new status for the finding
    /// </summary>
    public FindingStatus Status { get; set; }

    /// <summary>
    /// Optional notes about the status change
    /// </summary>
    public string? StatusChangeNotes { get; set; }
}
