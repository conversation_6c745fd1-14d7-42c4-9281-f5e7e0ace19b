using Asp.Versioning;
using HWSAuditPlatform.ApiService.Controllers;
using HWSAuditPlatform.ApiService.Models;
using HWSAuditPlatform.Application.Common;
// using HWSAuditPlatform.Application.Users.Commands.CreateProcessOwnerAssignment;
using HWSAuditPlatform.Application.Users.DTOs;
// using HWSAuditPlatform.Application.Users.Queries.GetProcessOwnerAssignments;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace HWSAuditPlatform.ApiService.Controllers;

/// <summary>
/// API controller for managing process owner assignments
/// </summary>
[ApiController]
[ApiVersion("1.0")]
[Route("api/v{version:apiVersion}/process-owner-assignments")]
[Authorize(Policy = "SystemManagerOrAbove")]
public class ProcessOwnerAssignmentsController : BaseController
{
    public ProcessOwnerAssignmentsController(ILogger<ProcessOwnerAssignmentsController> logger)
        : base(logger)
    {
    }

    /// <summary>
    /// Get process owner assignments with optional filtering and pagination
    /// </summary>
    /// <param name="query">Query parameters for filtering and pagination</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Paged list of process owner assignments</returns>
    [HttpGet]
    [ProducesResponseType(typeof(ApiResponse<PaginatedResult<ProcessOwnerAssignmentDto>>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status403Forbidden)]
    public async Task<ActionResult<PaginatedResult<ProcessOwnerAssignmentDto>>> GetProcessOwnerAssignments(
        [FromQuery] GetProcessOwnerAssignmentsQuery query,
        CancellationToken cancellationToken)
    {
        try
        {
            var result = await Mediator.Send(query, cancellationToken);
            return Success(result);
        }
        catch (UnauthorizedAccessException ex)
        {
            Logger.LogWarning(ex, "Unauthorized access to process owner assignments");
            return Forbid();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error retrieving process owner assignments");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Create a new process owner assignment
    /// </summary>
    /// <param name="command">Command to create the assignment</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>ID of the created assignment</returns>
    [HttpPost]
    [ProducesResponseType(typeof(ApiResponse<int>), StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status403Forbidden)]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status409Conflict)]
    public async Task<ActionResult<int>> CreateProcessOwnerAssignment(
        [FromBody] CreateProcessOwnerAssignmentCommand command,
        CancellationToken cancellationToken)
    {
        try
        {
            var assignmentId = await Mediator.Send(command, cancellationToken);
            return Created(nameof(GetProcessOwnerAssignments), new { processOwnerUserId = command.ProcessOwnerUserId }, assignmentId, "Process owner assignment created successfully");
        }
        catch (UnauthorizedAccessException ex)
        {
            Logger.LogWarning(ex, "Unauthorized attempt to create process owner assignment");
            return Forbid();
        }
        catch (InvalidOperationException ex)
        {
            Logger.LogWarning(ex, "Invalid operation when creating process owner assignment");
            return Conflict(ex.Message);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error creating process owner assignment");
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Get process owner assignments for the current user (if they are a ProcessOwner)
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of assignments for the current user</returns>
    [HttpGet("my-assignments")]
    [Authorize(Policy = "ProcessOwnerOrAbove")]
    [ProducesResponseType(typeof(ApiResponse<List<ProcessOwnerAssignmentDto>>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(ApiResponse<object>), StatusCodes.Status403Forbidden)]
    public async Task<ActionResult<List<ProcessOwnerAssignmentDto>>> GetMyAssignments(
        CancellationToken cancellationToken)
    {
        try
        {
            var query = new GetProcessOwnerAssignmentsQuery
            {
                IsActive = true,
                PageSize = 1000 // Get all assignments for the user
            };

            var result = await Mediator.Send(query, cancellationToken);
            return Success(result.Items.ToList());
        }
        catch (UnauthorizedAccessException ex)
        {
            Logger.LogWarning(ex, "Unauthorized access to user assignments");
            return Forbid();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error retrieving user assignments");
            return BadRequest(ex.Message);
        }
    }
}
