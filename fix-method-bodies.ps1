# PowerShell script to comment out problematic method bodies that use missing Command/Query classes

$controllersPath = "src\HWSAuditPlatform.ApiService\Controllers"

# Define problematic classes and their replacement comments
$problemClasses = @{
    'CreateAreaResponsibilityCommand' = '// TODO: Replace with service call'
    'CreateCorrectiveActionCommand' = '// TODO: Replace with service call'
    'SubmitAuditAnswerCommand' = '// TODO: Replace with service call'
    'CreateFindingCategoryCommand' = '// TODO: Replace with service call'
    'GetProcessOwnerAssignmentsQuery' = '// TODO: Replace with service call'
    'CreateFindingCommand' = '// TODO: Replace with service call'
    'CreateProcessOwnerAssignmentCommand' = '// TODO: Replace with service call'
    'CreateTemplateAccessAssignmentCommand' = '// TODO: Replace with service call'
    'CreateFactoryCommand' = '// TODO: Replace with service call'
    'CreateAreaCommand' = '// TODO: Replace with service call'
    'CreateSubAreaCommand' = '// TODO: Replace with service call'
    'SyncUsersFromAdRequest' = '// TODO: Replace with service call'
    'SyncUsersFromAdResult' = '// TODO: Replace with service call'
    'UpdateFactoryCommand' = '// TODO: Replace with service call'
    'GetRecurringAuditSettingsQuery' = '// TODO: Replace with service call'
    'CreateAuditTemplateCommand' = '// TODO: Replace with service call'
    'CreateRecurringAuditSettingCommand' = '// TODO: Replace with service call'
    'UpdateAuditTemplateCommand' = '// TODO: Replace with service call'
    'UpdateRecurringAuditSettingCommand' = '// TODO: Replace with service call'
    'GenerateRecurringAuditsResult' = '// TODO: Replace with service call'
    'CreateTemplateVersionCommand' = '// TODO: Replace with service call'
    'AddQuestionCommand' = '// TODO: Replace with service call'
    'UpdateQuestionCommand' = '// TODO: Replace with service call'
    'AddQuestionOptionsCommand' = '// TODO: Replace with service call'
}

# Get all controller files
$controllerFiles = Get-ChildItem -Path $controllersPath -Filter "*.cs"

foreach ($file in $controllerFiles) {
    $filePath = $file.FullName
    $content = Get-Content $filePath -Raw
    $modified = $false
    
    foreach ($className in $problemClasses.Keys) {
        if ($content -match $className) {
            Write-Host "Processing $($file.Name) for $className..."
            
            # Replace instantiations with comments
            $pattern = "new\s+$className\s*\([^)]*\)"
            if ($content -match $pattern) {
                $content = $content -replace $pattern, "/* $($problemClasses[$className]) */ null"
                $modified = $true
            }
            
            # Replace variable declarations
            $pattern = "$className\s+\w+"
            if ($content -match $pattern) {
                $content = $content -replace $pattern, "/* $($problemClasses[$className]) */ var"
                $modified = $true
            }
        }
    }
    
    if ($modified) {
        Set-Content $filePath $content -NoNewline
        Write-Host "Modified $($file.Name)"
    }
}

Write-Host "Method body fixes completed!"
