namespace HWSAuditPlatform.Application.Scheduling.DTOs;

/// <summary>
/// Result DTO for recurring audit generation operations
/// </summary>
public class GenerateRecurringAuditsResult
{
    /// <summary>
    /// Number of recurring audit settings that were processed
    /// </summary>
    public int SettingsProcessed { get; set; }

    /// <summary>
    /// Number of audit instances that were generated
    /// </summary>
    public int AuditsGenerated { get; set; }

    /// <summary>
    /// Number of errors encountered during generation
    /// </summary>
    public int ErrorsEncountered { get; set; }

    /// <summary>
    /// List of generated audit IDs
    /// </summary>
    public List<string> GeneratedAuditIds { get; set; } = new();

    /// <summary>
    /// List of error messages encountered during generation
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// Indicates if the generation was successful (no errors)
    /// </summary>
    public bool IsSuccess => ErrorsEncountered == 0;

    /// <summary>
    /// Summary message of the generation result
    /// </summary>
    public string Summary => $"Processed {SettingsProcessed} settings, generated {AuditsGenerated} audits" +
                            (ErrorsEncountered > 0 ? $", encountered {ErrorsEncountered} errors" : "");
}
