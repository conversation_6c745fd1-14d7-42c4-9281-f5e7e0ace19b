using MediatR;
using Microsoft.AspNetCore.Mvc;
using HWSAuditPlatform.ApiService.Models;
// using HWSAuditPlatform.Application.Findings.Commands.CreateFindingCategory;
// using HWSAuditPlatform.Application.Findings.Commands.AssignFindingCategory;
using HWSAuditPlatform.Application.Findings.DTOs;
// using HWSAuditPlatform.Application.Findings.Queries.GetFindingCategories;

namespace HWSAuditPlatform.ApiService.Controllers;

/// <summary>
/// Controller for managing finding categorization
/// </summary>
[Route("api/v1/findings/categories")]
[Tags("Finding Categorization")]
public class FindingCategoriesController : BaseController
{
    public FindingCategoriesController(ILogger<FindingCategoriesController> logger)
        : base(logger)
    {
    }

    /// <summary>
    /// Gets finding categories with optional filtering
    /// </summary>
    /// <param name="auditTemplateId">Filter by specific audit template ID</param>
    /// <param name="isActive">Filter by active status</param>
    /// <param name="includeUsageStatistics">Include finding count statistics</param>
    /// <returns>List of finding categories</returns>
    [HttpGet]
    [ProducesResponseType(typeof(ApiResponse<List<FindingCategoryDto>>), 200)]
    public async Task<ActionResult<List<FindingCategoryDto>>> GetFindingCategories(
        [FromQuery] int? auditTemplateId = null,
        [FromQuery] bool? isActive = true,
        [FromQuery] bool includeUsageStatistics = false)
    {
        var query = new GetFindingCategoriesQuery
        {
            AuditTemplateId = auditTemplateId,
            IsActive = isActive,
            IncludeUsageStatistics = includeUsageStatistics
        };

        var result = await Mediator.Send(query);
        return Success(result, "Finding categories retrieved successfully");
    }

    /// <summary>
    /// Creates a new finding category
    /// </summary>
    /// <param name="command">Finding category creation data</param>
    /// <returns>Created finding category</returns>
    [HttpPost]
    [ProducesResponseType(typeof(ApiResponse<FindingCategoryDto>), 201)]
    [ProducesResponseType(typeof(ApiErrorResponse), 400)]
    [ProducesResponseType(typeof(ApiErrorResponse), 404)]
    [ProducesResponseType(typeof(ApiErrorResponse), 409)]
    public async Task<ActionResult<FindingCategoryDto>> CreateFindingCategory(
        [FromBody] CreateFindingCategoryCommand command)
    {
        var result = await Mediator.Send(command);
        return Created(nameof(GetFindingCategory), new { id = result.Id }, result, 
            "Finding category created successfully");
    }

    /// <summary>
    /// Gets a specific finding category by ID
    /// </summary>
    /// <param name="id">Finding category ID</param>
    /// <returns>Finding category details</returns>
    [HttpGet("{id:int}")]
    [ProducesResponseType(typeof(ApiResponse<FindingCategoryDto>), 200)]
    [ProducesResponseType(typeof(ApiErrorResponse), 404)]
    public async Task<ActionResult<FindingCategoryDto>> GetFindingCategory(int id)
    {
        var query = new GetFindingCategoriesQuery { IncludeUsageStatistics = true };
        var categories = await Mediator.Send(query);
        var category = categories.FirstOrDefault(c => c.Id == id);

        if (category == null)
        {
            return NotFound(ApiErrorResponse.NotFound($"Finding category with ID '{id}' was not found."));
        }

        return Success(category, "Finding category retrieved successfully");
    }

    /// <summary>
    /// Updates an existing finding category
    /// </summary>
    /// <param name="id">Finding category ID</param>
    /// <param name="command">Updated finding category data</param>
    /// <returns>Updated finding category</returns>
    [HttpPut("{id:int}")]
    [ProducesResponseType(typeof(ApiResponse<FindingCategoryDto>), 200)]
    [ProducesResponseType(typeof(ApiErrorResponse), 400)]
    [ProducesResponseType(typeof(ApiErrorResponse), 404)]
    public async Task<ActionResult<FindingCategoryDto>> UpdateFindingCategory(
        int id, [FromBody] CreateFindingCategoryCommand command)
    {
        // For now, we'll implement this as a placeholder
        // In a full implementation, you'd want a dedicated UpdateFindingCategoryCommand
        Logger.LogInformation("Update finding category {Id} requested", id);
        
        return BadRequest(ApiErrorResponse.BadRequest("Update functionality not yet implemented. Please delete and recreate the category."));
    }

    /// <summary>
    /// Deletes a finding category
    /// </summary>
    /// <param name="id">Finding category ID</param>
    /// <returns>Success confirmation</returns>
    [HttpDelete("{id:int}")]
    [ProducesResponseType(typeof(ApiResponse<object>), 200)]
    [ProducesResponseType(typeof(ApiErrorResponse), 404)]
    [ProducesResponseType(typeof(ApiErrorResponse), 409)]
    public async Task<ActionResult> DeleteFindingCategory(int id)
    {
        // This would need a DeleteFindingCategoryCommand in a full implementation
        Logger.LogInformation("Delete finding category {Id} requested", id);
        
        return BadRequest(ApiErrorResponse.BadRequest("Delete functionality not yet implemented."));
    }

    /// <summary>
    /// Gets category statistics for an audit template
    /// </summary>
    /// <param name="auditTemplateId">Audit template ID</param>
    /// <returns>Category usage statistics</returns>
    [HttpGet("templates/{auditTemplateId:int}/statistics")]
    [ProducesResponseType(typeof(ApiResponse<object>), 200)]
    [ProducesResponseType(typeof(ApiErrorResponse), 404)]
    public async Task<ActionResult<object>> GetCategoryStatistics(int auditTemplateId)
    {
        var query = new GetFindingCategoriesQuery 
        { 
            AuditTemplateId = auditTemplateId,
            IncludeUsageStatistics = true 
        };

        var categories = await Mediator.Send(query);
        
        if (!categories.Any())
        {
            return NotFound(ApiErrorResponse.NotFound($"No categories found for audit template {auditTemplateId}."));
        }

        var statistics = new
        {
            TotalCategories = categories.Count,
            ActiveCategories = categories.Count(c => c.IsActive),
            TotalFindings = categories.Sum(c => c.FindingCount),
            CategorizedFindings = categories.Sum(c => c.FindingCount),
            UncategorizedFindings = 0, // Would need additional query to calculate
            CategoryUsage = categories.ToDictionary(c => c.Id, c => new
            {
                CategoryId = c.Id,
                CategoryName = c.CategoryName,
                FindingCount = c.FindingCount,
                OpenFindingCount = c.OpenFindingCount,
                ClosedFindingCount = c.FindingCount - c.OpenFindingCount,
                UsagePercentage = categories.Sum(cat => cat.FindingCount) > 0 
                    ? (double)c.FindingCount / categories.Sum(cat => cat.FindingCount) * 100 
                    : 0
            })
        };

        return Success(statistics, "Category statistics retrieved successfully");
    }
}

/// <summary>
/// Controller for assigning categories to findings
/// </summary>
[Route("api/v1/findings")]
[Tags("Finding Categorization")]
public class FindingCategoryAssignmentController : BaseController
{
    public FindingCategoryAssignmentController(ILogger<FindingCategoryAssignmentController> logger)
        : base(logger)
    {
    }

    /// <summary>
    /// Assigns or removes a category from a finding
    /// </summary>
    /// <param name="findingId">Finding ID</param>
    /// <param name="request">Category assignment request</param>
    /// <returns>Success confirmation</returns>
    [HttpPut("{findingId}/category")]
    [ProducesResponseType(typeof(ApiResponse<object>), 200)]
    [ProducesResponseType(typeof(ApiErrorResponse), 400)]
    [ProducesResponseType(typeof(ApiErrorResponse), 404)]
    [ProducesResponseType(typeof(ApiErrorResponse), 409)]
    public async Task<ActionResult> AssignFindingCategory(
        string findingId, 
        [FromBody] AssignFindingCategoryRequest request)
    {
        var command = new AssignFindingCategoryCommand
        {
            FindingId = findingId,
            FindingCategoryId = request.FindingCategoryId
        };

        var success = await Mediator.Send(command);

        if (!success)
        {
            return BadRequest(ApiErrorResponse.BadRequest("Failed to assign category to finding."));
        }

        var message = request.FindingCategoryId.HasValue 
            ? "Finding category assigned successfully" 
            : "Finding category removed successfully";

        return Success(message);
    }
}

/// <summary>
/// Request model for assigning finding categories
/// </summary>
public class AssignFindingCategoryRequest
{
    /// <summary>
    /// Category ID to assign (null to remove category)
    /// </summary>
    public int? FindingCategoryId { get; set; }
}
