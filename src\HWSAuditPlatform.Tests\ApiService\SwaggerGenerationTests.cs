using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Swashbuckle.AspNetCore.SwaggerGen;
using Swashbuckle.AspNetCore.Swagger;
using System.Net.Http;
using System.Threading.Tasks;
using Xunit;
using Xunit.Abstractions;

namespace HWSAuditPlatform.Tests.ApiService;

/// <summary>
/// Tests to verify Swagger/OpenAPI schema generation works correctly
/// </summary>
public class SwaggerGenerationTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;
    private readonly ITestOutputHelper _output;

    public SwaggerGenerationTests(WebApplicationFactory<Program> factory, ITestOutputHelper output)
    {
        _factory = factory;
        _output = output;
    }

    [Fact]
    public void SwaggerSchemaGeneration_ShouldNotHaveDuplicateSchemaIds()
    {
        // Arrange
        var services = new ServiceCollection();
        services.AddSwaggerGen();

        var serviceProvider = services.BuildServiceProvider();
        var swaggerProvider = serviceProvider.GetRequiredService<ISwaggerProvider>();

        // Act & Assert
        try
        {
            // This should not throw an exception about duplicate schema IDs
            var swaggerDoc = swaggerProvider.GetSwagger("v1");

            Assert.NotNull(swaggerDoc);
            Assert.NotNull(swaggerDoc.Components?.Schemas);

            // Verify that both CorrectiveActionSummaryDto classes can coexist with different schema IDs
            var schemaKeys = swaggerDoc.Components.Schemas.Keys.ToList();

            _output.WriteLine($"Generated {schemaKeys.Count} schemas:");
            foreach (var key in schemaKeys.OrderBy(k => k))
            {
                _output.WriteLine($"  - {key}");
            }

            // The test passes if we reach this point without exceptions
            _output.WriteLine("Schema generation completed successfully without duplicate ID conflicts!");
        }
        catch (Exception ex)
        {
            _output.WriteLine($"Exception: {ex.Message}");
            _output.WriteLine($"Stack Trace: {ex.StackTrace}");
            throw;
        }
    }

    [Fact]
    public async Task SwaggerJson_ShouldGenerateWithoutErrors()
    {
        // Arrange
        var client = _factory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureServices(services =>
            {
                // Override problematic services for testing
                services.Configure<LoggerFilterOptions>(options =>
                {
                    options.MinLevel = LogLevel.Warning;
                });
            });
            builder.UseEnvironment("Development"); // Use Development to enable Swagger
        }).CreateClient();

        // Act & Assert
        try
        {
            var response = await client.GetAsync("/swagger/v1/swagger.json");

            _output.WriteLine($"Response Status: {response.StatusCode}");

            if (!response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                _output.WriteLine($"Response Content: {content}");
            }

            // The main test - this should not throw an exception about duplicate schema IDs
            response.EnsureSuccessStatusCode();

            var swaggerJson = await response.Content.ReadAsStringAsync();
            Assert.NotEmpty(swaggerJson);
            Assert.Contains("\"openapi\":", swaggerJson);

            _output.WriteLine("Swagger JSON generated successfully!");
        }
        catch (Exception ex)
        {
            _output.WriteLine($"Exception: {ex.Message}");
            _output.WriteLine($"Stack Trace: {ex.StackTrace}");
            throw;
        }
    }

    [Fact]
    public void SwaggerGenerator_ShouldNotHaveDuplicateSchemaIds()
    {
        // Arrange
        var serviceProvider = _factory.Services;
        var swaggerGenerator = serviceProvider.GetRequiredService<ISwaggerProvider>();

        // Act & Assert - This should not throw an exception about duplicate schema IDs
        var exception = Record.Exception(() =>
        {
            var swagger = swaggerGenerator.GetSwagger("v1");
            Assert.NotNull(swagger);
        });

        if (exception != null)
        {
            _output.WriteLine($"Exception during schema generation: {exception.Message}");
            _output.WriteLine($"Stack Trace: {exception.StackTrace}");
        }

        Assert.Null(exception);
    }
}
