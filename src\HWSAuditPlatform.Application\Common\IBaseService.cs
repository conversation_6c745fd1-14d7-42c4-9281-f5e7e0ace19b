using HWSAuditPlatform.Application.Interfaces;

namespace HWSAuditPlatform.Application.Common;

/// <summary>
/// Base interface for application services
/// </summary>
public interface IBaseService
{
}

/// <summary>
/// Base interface for application services with common dependencies
/// </summary>
public interface IBaseService<TContext> : IBaseService
    where TContext : IApplicationDbContext
{
    /// <summary>
    /// Gets the database context
    /// </summary>
    protected TContext Context { get; }
}
