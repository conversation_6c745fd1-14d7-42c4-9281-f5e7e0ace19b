<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
	  <PreserveCompilationContext>true</PreserveCompilationContext>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Aspire.Hosting.Testing" />
    <PackageReference Include="coverlet.collector" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" />
    <PackageReference Include="xunit" />
    <PackageReference Include="xunit.runner.visualstudio" />
    <PackageReference Include="FluentAssertions" />
    <PackageReference Include="Moq" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" />
    <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" />
    <PackageReference Include="Testcontainers.MsSql" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" />
    <PackageReference Include="Blazored.LocalStorage" />
    <PackageReference Include="bunit" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\HWSAuditPlatform.AppHost\HWSAuditPlatform.AppHost.csproj" />
    <ProjectReference Include="..\HWSAuditPlatform.Domain\HWSAuditPlatform.Domain.csproj" />
    <ProjectReference Include="..\HWSAuditPlatform.Application\HWSAuditPlatform.Application.csproj" />
    <ProjectReference Include="..\HWSAuditPlatform.Infrastructure\HWSAuditPlatform.Infrastructure.csproj" />
    <ProjectReference Include="..\HWSAuditPlatform.ApiService\HWSAuditPlatform.ApiService.csproj" />
    <ProjectReference Include="..\HWSAuditPlatform.WebApp\HWSAuditPlatform.WebApp.csproj" />
    <ProjectReference Include="..\HWSAuditPlatform.WebAuditPWA\HWSAuditPlatform.WebAuditPWA.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Using Include="System.Net" />
    <Using Include="Microsoft.Extensions.DependencyInjection" />
    <Using Include="Aspire.Hosting.ApplicationModel" />
    <Using Include="Aspire.Hosting.Testing" />
    <Using Include="Xunit" />
  </ItemGroup>
</Project>